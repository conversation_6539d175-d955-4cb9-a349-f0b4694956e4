package net.lab1024.sa.admin.common;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.system.login.domain.RequestEmployee;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.controller.SupportBaseController;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

/**
 * admin 端基础 service
 *
 * <AUTHOR> 卓大
 * @Date 2022-09-22 19:48:51
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Slf4j
public class AdminBaseService extends SupportBaseController {

    /**
     * 获取当前登录人信息
     *
     * @return
     */
    public RequestEmployee getRequestEmployee() {
        return AdminRequestUtil.getRequestUser();
    }

    /**
     * 获取当前登录人id
     *
     * @return
     */
    public Long getRequestUserId() {
        return SmartRequestUtil.getRequestUserId();
    }
} 