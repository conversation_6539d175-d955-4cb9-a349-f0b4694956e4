package net.lab1024.sa.admin.module.business.nfc.userdevices.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.nfc.userdevices.domain.form.UserDevicesQueryForm;
import net.lab1024.sa.admin.module.business.nfc.userdevices.domain.vo.UserDevicesVO;
import net.lab1024.sa.admin.module.business.nfc.userdevices.service.UserDevicesService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户登录设备表 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:47:54
 * @Copyright xueh所有
 */

@RestController
@Tag(name = "用户登录设备表")
public class UserDevicesController {

    @Resource
    private UserDevicesService userDevicesService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/userDevices/queryPage")
    @SaCheckPermission("userDevices:query")
    public ResponseDTO<PageResult<UserDevicesVO>> queryPage(@RequestBody @Valid UserDevicesQueryForm queryForm) {
        return ResponseDTO.ok(userDevicesService.queryPage(queryForm));
    }


}
