package net.lab1024.sa.admin.module.business.nfc.userdevices.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 用户登录设备表 实体类
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:47:54
 * @Copyright xueh所有
 */

@Data
@TableName("t_user_devices")
public class UserDevicesEntity {

    /**
     * 记录唯一ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的用户ID
     */
    private Long userId;

    /**
     * 设备的唯一标识符 (如Android ID)
     */
    private String deviceId;

    /**
     * 设备型号 (如: 'Google Pixel 8')
     */
    private String deviceModel;

    /**
     * 最后登录的IP地址
     */
    private String lastLoginIp;

    /**
     * 根据IP解析的大致地理位置
     */
    private String lastLoginLocation;

    /**
     * 最后一次登录时间
     */
    private LocalDateTime lastLoginAt;

    /**
     * 首次记录该设备的时间
     */
    private LocalDateTime createdAt;

}
