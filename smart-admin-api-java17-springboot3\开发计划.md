**
核心开发哲学： 后端接口先行，前端界面跟随，功能逐个闭环，绝不贪多。

---

阶段零：环境搭建与项目“点火”

~~🎯 目标： 确保您的电脑成为一个合格的“开发工坊”，让前后端两个项目都能成功运行起来。
- [ ] 1. 开发软件安装：
    - 指导： 这是我们的“总工作台”，请确保已从官网下载并安装完毕。
        - Android Studio (Hedgehog 或更高版本): 用于开发安卓App。
        - IntelliJ IDEA Community/Ultimate: 用于开发Java后端（比用AS开发Java体验好得多）。
        - MySQL 8.0 数据库: 作为我们的数据仓库。
        - DBeaver 或 Navicat: 数据库可视化客户端，方便我们查看数据。
- [ ] 2. 后端项目 (smart-admin) “点火”：
    - 指导： 这一步的目标是让后端服务在您的电脑上跑起来。
        1. 用IntelliJ IDEA打开smart-admin项目。
        2. 打开src/main/resources/application.yml文件，找到datasource部分，修改url, username, password以匹配您本地的MySQL配置。
        3. 在您的MySQL中，手动创建一个空的数据库，名字叫smart_admin_v3。
        4. 找到并运行项目的主启动类（通常是SaAdminApplication.java），点击类名旁边的绿色三角形“播放”按钮。
        5. 观察控制台，如果最后没有报红字，并显示了端口号（如8080），说明后端点火成功！ smart-admin会自动帮我们创建大部分表格。
- [ ] 3. 前端项目 (android-showcase) “点火”：
    - 指导： 这一步的目标是让安卓App的“空壳子”在模拟器上跑起来。
        1. 用Android Studio打开android-showcase项目。
        2. 执行“版本修复”手术：
        - 打开**:app**模块的build.gradle.kts，将compileSdk和targetSdk都改为 36。
        - 打开根目录的gradle/libs.versions.toml，找到[versions]部分，将compose-compiler的版本改为 "1.5.10"（为了匹配kotlin = "1.9.25"）。
        3. 点击工具栏的“Sync Now”或大象图标，等待Gradle同步完成。
        4. 创建一个安卓模拟器（如我们之前讨论的）。
        5. 点击绿色的“播放”按钮运行app模块。
        6. 如果在模拟器上看到了android-showcase的主界面，说明前端点火成功！
- [ ] 4. 数据库“精装修”：
    - 指导： 现在我们需要根据我们的业务，改造和创建表格。
        1. 打开DBeaver/Navicat，连接到您的smart_admin_v3数据库。
        2. 执行改造SQL： 运行上一轮我们确认的ALTER TABLE t_employee ...语句，为t_employee表增加uid和role_id字段。
        3. 执行新建SQL： 运行上一轮我们确认的CREATE TABLE ...语句，创建全新的transactions和user_devices表。
        4. （可选）清理： 您可以手动删除那些我们用不上的表（如t_goods, t_oa_enterprise等），让数据库更清爽。
           恭喜！准备工作完成！您的开发环境和项目基础已经就绪，这是一个巨大的里程碑！

---

✅ 阶段一：用户认证模块 (打通任督二脉)

🎯 目标： 实现完整的用户注册、登录功能。
- [ ] 1. 后端接口开发 (IntelliJ IDEA):
    - 任务： 创建并实现用户注册和登录的API。
    - 指导：
        1. 修改实体类: 打开net.lab1024.sa.base.module.support.employee.domain.entity.EmployeeEntity，为它添加uid和roleId两个属性。
        2. 创建DTO: 在domain.dto包下，创建UserRegisterDTO和UserLoginDTO两个类，用来接收前端传来的JSON数据。
        3. 开发Service: 在EmployeeService中，创建register(UserRegisterDTO)方法。内部逻辑是：检查手机号是否已存在 -> 将密码用passwordEncoder.encode()加密 -> 创建新的EmployeeEntity对象并保存到数据库。
        4. 开发Controller: 在EmployeeController中，创建两个公开的（无需登录的）API端点：
        - @PostMapping("/auth/register"): 调用register service方法。
        - @PostMapping("/auth/login"): 调用Sa-Token的StpUtil.login()方法，成功后返回生成的Token。
        5. 【自测】: 运行后端，使用Swagger文档 (/doc.html) 或 Postman，手动测试这两个接口，确保能成功创建用户并登录获取Token。~~
- [ ] 2. 前端界面与逻辑 (Android Studio):
    - 任务： 创建登录注册页面，并与后端接口联调。
    - 指导：
        1. 创建新模块: 在项目中创建新的feature模块，如:feature-auth。
        2. 定义API接口: 在一个AuthApiService.kt文件中，使用Retrofit的注解定义@POST("auth/register")和@POST("auth/login")两个接口函数。
        3. 实现ViewModel: 创建AuthViewModel.kt，注入一个Repository。在ViewModel中创建register()和login()两个方法，内部调用Repository的方法来发起网络请求，并通过StateFlow来暴露UI状态（如“加载中”、“登录成功”、“错误信息”）。
        4. 构建UI: 创建LoginScreen.kt和RegisterScreen.kt两个Composable函数。UI中包含TextField用于输入，Button用于提交。按钮的onClick事件会调用viewModel中对应的方法。
        5. 保存Token: 监听login()方法返回的“登录成功”状态，一旦成功，立即将返回的JWT Token字符串保存到Jetpack DataStore中。
- [ ] 🎯 里程碑 #1: 前后端完整联调。 在模拟器上打开App，输入手机号和密码，点击注册，能在DBeaver里看到t_employee表多了一条新数据。然后返回登录页，输入刚注册的账号密码，能成功登录并跳转到App主页。

---

✅ 阶段二：NFC中继核心模块 (攻克技术难关)

🎯 目标： 实现核心的、账户驱动的、双向实时中继功能。
- [ ] 1. 后端WebSocket开发 (IntelliJ IDEA):
    - 任务： 搭建WebSocket服务，实现会话管理和消息路由。
    - 指导：
        1. 重构WebSocket模块: 找到smart-admin中现有的WebSocket实现，参考其WebSocketConfig和WebSocketEndpoint，但要创建一个全新的NfcRelayEndpoint。
        2. 创建会话管理器: 创建一个@Component注解的单例NfcSessionManager。其核心是private final Map<Long, UserActiveSession> sessionMap = new ConcurrentHashMap<>();。
        3. 实现核心逻辑: 在NfcRelayEndpoint的@OnMessage方法中，解析收到的JSON消息。
        - type为REGISTER_ROLE时，调用NfcSessionManager的方法，将当前用户的WebSocketSession和role存入sessionMap。然后检查是否配对成功，如果成功，向双方发送PAIRED消息。
        - type为APDU_COMMAND或APDU_RESPONSE时，从sessionMap中找到该用户的“伙伴”Session，并将消息体原封不动地转发过去。
        4. 实现@OnClose: 当连接关闭时，必须从sessionMap中移除该用户的所有信息，并通知其伙伴（如果存在）连接已断开。
        5. 实现transactions表操作: 在配对成功时，向transactions表插入一条新记录；在会话结束时，更新这条记录的状态和结束时间。
- [ ] 2. 前端NFC与WebSocket集成 (Android Studio):
    - 任务： 将设备NFC能力与WebSocket客户端深度绑定。这是前端的重头戏。
    - 指导：
        1. 创建Relay模块: 创建:feature-relay模块。
        2. 创建WebSocketManager: 创建一个单例的WebSocketManager.kt，使用OkHttp的WebSocketListener。它需要有connect(token), disconnect(), sendMessage(json)等方法，并通过SharedFlow向外暴露接收到的服务器消息。
        3. 封装NFC服务: 创建一个:core-nfc模块。将官方的CardReader和HostApduService示例代码移植进来。
        - 修改HostApduService的processCommandApdu方法：收到POS机的APDU后，不是在本地处理，而是调用WebSocketManager.sendMessage()将其发送给后端。
        - 修改CardReader的回调：当从物理卡读到响应APDU后，同样调用WebSocketManager.sendMessage()发送给后端。
        4. 创建RelayViewModel: 这是连接一切的“大脑”。它负责：
        - 在页面创建时，调用WebSocketManager.connect()。
        - 监听WebSocketManager收到的消息，并更新UI状态（如显示“已配对”）。
        - 当收到服务器转发来的APDU_COMMAND时，将指令传递给CardReader服务去执行。
- [ ] 🎯 里程碑 #2: 核心功能联调。 在两台手机上登录同一个账号，一台选“读卡端”，一台选“模拟端”。能看到UI状态都变为“连接成功”。将一张NFC卡贴近读卡端手机，同时将模拟端手机靠近一个真实的POS机或NFC读卡器，能在后端的控制台日志中看到APDU数据被成功转发。

---

✅ 阶段三 & 四：交易历史与MVP收尾

- 指导： 这部分是标准的CRUD功能，在您完成了前两个阶段后，做起来会相对轻松。请参考上一个Checklist中的任务列表，按部就班地完成API开发、UI构建和联调即可。
- [ ] 🎯 里程碑 #3: 在App中完成一次NFC中继后，进入“交易历史”页面，能看到刚刚产生的这条新记录。
- [ ] 🎯 最终里程碑: 所有功能联调通过，您可以编译出一个APK文件，发给朋友测试。您的MVP正式完成！
  这个超详细的清单涵盖了从环境到编码、再到测试的每一个关键节点和思路。这是一个不小的挑战，但只要您按照这个路线图，耐心、专注地完成每一个[ ]，您一定能独立构建出这个了不起的系统。祝您开发顺利！**