<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.config.ConfigDao">
    <!-- 分页查询系统配置 -->
    <select id="queryByPage" resultType="net.lab1024.sa.base.module.support.config.domain.ConfigEntity">
        SELECT *
        FROM t_config
        <where>
            <if test="query.configKey != null and query.configKey != ''">
                AND INSTR(config_key,#{query.configKey})
            </if>
        </where>
    </select>

    <!-- 根据key查询获取数据 -->
    <select id="selectByKey" resultType="net.lab1024.sa.base.module.support.config.domain.ConfigEntity">
        SELECT *
        FROM t_config
        WHERE config_key = #{key}
    </select>

</mapper>