package net.lab1024.sa.admin.module.business.nfc.ws;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import jakarta.websocket.Session;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * {@link NfcSessionManager} 的单元测试.
 *
 * <AUTHOR>
 */
@DisplayName("NfcSessionManager 单元测试")
class NfcSessionManagerTest {

    private NfcSessionManager nfcSessionManager;
    private Session sessionTransmitter;
    private Session sessionReceiver;
    private static final Long TEST_USER_ID = 1L;

    @BeforeEach
    void setUp() {
        // 为每个测试实例化一个新的管理器，确保隔离性
        nfcSessionManager = new NfcSessionManager();

        // 使用 Mockito 创建模拟的 WebSocket 会话
        sessionTransmitter = Mockito.mock(Session.class);
        sessionReceiver = Mockito.mock(Session.class);

        // 模拟 getUserProperties() 的行为，返回一个可变的 map
        when(sessionTransmitter.getUserProperties()).thenReturn(new ConcurrentHashMap<>(Map.of("employeeId", TEST_USER_ID)));
        when(sessionReceiver.getUserProperties()).thenReturn(new ConcurrentHashMap<>(Map.of("employeeId", TEST_USER_ID)));
    }

    @Nested
    @DisplayName("注册与配对逻辑")
    class RegistrationTests {

        @Test
        @DisplayName("指定一个用户，当第一个设备注册时，会话应处于等待状态")
        void givenUser_whenFirstDeviceRegisters_thenSessionIsWaiting() {
            // 操作
            NfcSessionManager.PairingResult result = nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, sessionTransmitter);

            // 断言
            assertFalse(result.isPaired(), "只有一个设备时，配对不应完成");
            assertNull(result.partnerSession(), "未配对时，伙伴会话应为 null");
        }
        
        @Test
        @DisplayName("指定一个已有设备在等待的用户，当第二个设备注册时，应配对成功")
        void givenWaitingUser_whenSecondDeviceRegisters_thenPairingIsSuccessful() {
            // 准备：第一个设备已注册并在等待
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, sessionTransmitter);

            // 操作：同一用户的第二个设备注册
            NfcSessionManager.PairingResult result = nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.RECEIVER, sessionReceiver);

            // 断言
            assertTrue(result.isPaired(), "第二个设备注册后，配对应该完成");
            assertNotNull(result.partnerSession(), "配对后，伙伴会话不应为 null");
            assertEquals(sessionTransmitter, result.partnerSession(), "伙伴会话应该是发送端的会话");
        }

        @Test
        @DisplayName("指定一个已完全配对的用户，当第三个设备尝试注册时，应抛出 SessionAlreadyActiveException")
        void givenPairedUser_whenThirdDeviceRegisters_thenThrowsSessionAlreadyActiveException() {
            // 准备：一个用户已完全配对
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, sessionTransmitter);
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.RECEIVER, sessionReceiver);
            
            // 操作与断言
            Session thirdSession = Mockito.mock(Session.class);
            assertThrows(SessionAlreadyActiveException.class, () -> {
                nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, thirdSession);
            }, "为已配对用户注册第三个设备应抛出异常");
        }
    }

    @Nested
    @DisplayName("会话生命周期与清理")
    class LifecycleTests {

        @Test
        @DisplayName("指定一个已配对的用户, getPartnerSession 应为每个设备返回正确的伙伴")
        void givenPairedUser_whenGetPartner_thenReturnsCorrectPartner() {
            // 准备
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, sessionTransmitter);
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.RECEIVER, sessionReceiver);

            // 操作与断言
            assertEquals(sessionReceiver, nfcSessionManager.getPartnerSession(sessionTransmitter), "发送端的伙伴应为接收端");
            assertEquals(sessionTransmitter, nfcSessionManager.getPartnerSession(sessionReceiver), "接收端的伙伴应为发送端");
        }

        @Test
        @DisplayName("指定一个已配对的用户, 在移除两个会话后, 该用户的条目应从 map 中清除")
        void givenPairedUser_whenRemoveBothDevices_thenMapNoLongerContainsUser() {
            // 准备
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, sessionTransmitter);
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.RECEIVER, sessionReceiver);
            nfcSessionManager.setTransactionId(TEST_USER_ID, 100L);

            // 操作
            nfcSessionManager.removeSession(sessionTransmitter);
            nfcSessionManager.removeSession(sessionReceiver);

            // 断言
            assertNull(nfcSessionManager.getPartnerSession(sessionTransmitter), "清理后不应再有伙伴");
            assertNull(nfcSessionManager.getTransactionId(TEST_USER_ID), "用户的条目应被完全移除");
        }

        @Test
        @DisplayName("指定一个已配对的用户, 移除一个会话后, 其 transactionId 应在其属性中可用")
        void givenSessionWithTransactionId_whenRemove_thenTransactionIdIsPropagated() {
            // 准备
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, sessionTransmitter);
            nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.RECEIVER, sessionReceiver);
            nfcSessionManager.setTransactionId(TEST_USER_ID, 12345L);

            // 操作
            nfcSessionManager.removeSession(sessionTransmitter);

            // 断言
            assertEquals(12345L, sessionTransmitter.getUserProperties().get("transactionId"), "移除会话时，transactionId 应被传递到会话属性中");
        }
    }

    @Nested
    @DisplayName("并发安全")
    class ConcurrencyTests {

        @Test
        @DisplayName("指定对同一用户的并发注册请求, 最终状态应保持一致并成功配对")
        void givenMultiThreadedRequestsForSameUser_whenRegister_thenStateIsConsistent() throws InterruptedException {
            // 准备
            int numberOfThreads = 2;
            ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);
            CountDownLatch latch = new CountDownLatch(numberOfThreads);

            // 操作: 模拟两个设备在完全相同的时间为同一用户注册
            executorService.submit(() -> {
                try {
                    nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.TRANSMITTER, sessionTransmitter);
                } finally {
                    latch.countDown();
                }
            });
            executorService.submit(() -> {
                try {
                    nfcSessionManager.register(TEST_USER_ID, NfcSessionManager.Role.RECEIVER, sessionReceiver);
                } finally {
                    latch.countDown();
                }
            });

            // 等待两个线程完成
            latch.await(5, TimeUnit.SECONDS);
            executorService.shutdown();

            // 断言: 即使在并发下，最终状态也应该是一个干净、配对成功的会话
            Session transmitterPartner = nfcSessionManager.getPartnerSession(sessionTransmitter);
            Session receiverPartner = nfcSessionManager.getPartnerSession(sessionReceiver);

            assertNotNull(transmitterPartner, "并发注册后，发送端应该有一个伙伴");
            assertNotNull(receiverPartner, "并发注册后，接收端应该有一个伙伴");
            assertEquals(sessionReceiver, transmitterPartner, "发送端的伙伴应为接收端");
            assertEquals(sessionTransmitter, receiverPartner, "接收端的伙伴应为发送端");
        }
    }
} 