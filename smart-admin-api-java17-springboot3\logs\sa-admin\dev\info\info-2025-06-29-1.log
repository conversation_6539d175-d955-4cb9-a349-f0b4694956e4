[2025-06-29 07:15:31,549][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 07:15:31,581][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 35780 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-29 07:15:31,584][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-29 07:15:32,422][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 07:15:32,423][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 07:15:32,449][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
[2025-06-29 07:15:33,484][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 07:15:34,710][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-29 07:15:34,723][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-29 07:15:34,725][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-29 07:15:34,725][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-29 07:15:34,795][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-29 07:15:34,795][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 3178 ms 
[2025-06-29 07:15:35,403][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 07:15:35,805][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 07:15:36,100][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 07:15:36,775][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 07:15:37,939][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 07:15:38,320][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 07:15:39,561][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 07:15:39,657][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-29 07:15:39,670][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-29 07:15:39,720][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 8.596 seconds (process running for 9.279) 
[2025-06-29 07:15:40,341][INFO ][RMI TCP Connection(3)-192.168.50.194][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-29 07:15:40,341][INFO ][RMI TCP Connection(3)-192.168.50.194][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-29 07:15:40,343][INFO ][RMI TCP Connection(3)-192.168.50.194][o.s.w.s.DispatcherServlet:554] Completed initialization in 2 ms 
[2025-06-29 07:15:47,942][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-29 07:15:47,942][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-29 07:15:47,942][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:16:47,963][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:17:48,009][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:17:48,366][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 07:18:48,043][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:19:48,084][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:19:48,704][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 07:20:48,117][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:21:48,146][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:21:49,061][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->109ms 
[2025-06-29 07:22:48,178][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:23:48,221][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:23:49,395][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-29 07:24:48,246][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:25:48,268][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:25:49,742][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 07:26:48,299][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:27:48,324][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:27:50,100][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->111ms 
[2025-06-29 07:28:48,348][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:29:48,386][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:29:50,420][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-29 07:30:48,410][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:31:48,445][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:31:50,742][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 07:32:48,476][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:33:48,532][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:33:51,057][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 07:34:48,556][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:35:48,585][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:35:51,397][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-29 07:36:48,611][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:37:48,648][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:37:51,701][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-29 07:38:48,668][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:39:48,734][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:39:52,013][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->94ms 
[2025-06-29 07:40:48,756][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:41:48,784][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:41:52,334][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 07:42:48,806][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:43:48,847][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:43:52,657][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->91ms 
[2025-06-29 07:44:48,866][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:45:48,887][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:45:52,968][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-29 07:46:48,910][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:47:48,945][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:47:53,276][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->90ms 
[2025-06-29 07:48:48,961][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:49:49,004][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:49:53,823][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->194ms 
[2025-06-29 07:50:49,025][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:51:49,043][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:51:54,179][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-29 07:52:49,070][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:53:49,105][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:53:54,480][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->90ms 
[2025-06-29 07:54:49,120][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:55:49,137][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:55:54,791][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-29 07:56:49,158][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:57:49,196][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:58:49,216][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:59:49,259][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 07:59:55,237][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 08:00:49,277][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:01:49,303][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:01:55,544][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-29 08:02:49,323][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:03:36,746][INFO ][http-nio-1024-exec-7][o.a.c.h.Http11Processor:175] Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level. 
java.lang.IllegalArgumentException: Invalid character found in method name ["loginDevice": ]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:407) ~[tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:262) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:904) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) [tomcat-embed-core-10.1.25.jar:10.1.25]
	at java.base/java.lang.Thread.run(Thread.java:842) [?:?]
[2025-06-29 08:03:49,357][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:03:55,856][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-29 08:04:49,383][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:05:49,406][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:06:49,433][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:07:49,464][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:07:56,248][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-29 08:08:49,491][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:09:49,529][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:09:56,567][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-29 08:10:49,557][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:11:49,579][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:11:56,864][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->90ms 
[2025-06-29 08:12:49,595][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:13:49,634][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:14:49,658][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:15:10,185][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-29 08:15:49,690][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:15:57,275][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-29 08:16:49,708][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:17:49,741][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:17:57,603][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-29 08:18:49,780][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:19:49,805][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:19:57,917][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-29 08:20:49,833][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:21:49,859][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:22:49,882][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:23:49,904][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:23:58,289][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-29 08:24:49,920][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:25:49,936][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:25:58,595][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-29 08:26:49,964][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:27:50,001][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:27:58,945][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->91ms 
[2025-06-29 08:28:50,022][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:29:50,060][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:29:59,251][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->91ms 
[2025-06-29 08:30:50,089][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:31:50,108][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:31:59,545][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->91ms 
[2025-06-29 08:32:50,138][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:33:50,168][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:33:59,845][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->91ms 
[2025-06-29 08:34:50,192][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:35:50,219][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:36:50,236][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:37:50,259][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:38:00,230][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->94ms 
[2025-06-29 08:38:50,288][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:39:50,320][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:40:00,550][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-29 08:40:50,349][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:42:27,651][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:42:27,922][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-29 08:43:27,680][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:44:27,714][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:45:27,736][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:46:27,757][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:46:28,302][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->94ms 
[2025-06-29 08:47:27,791][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:47:58,291][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-29 08:47:58,320][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 08:47:58,321][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-29 08:48:01,465][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 08:48:01,491][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 56632 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-29 08:48:01,493][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-29 08:48:02,782][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 08:48:02,783][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 08:48:02,809][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces. 
[2025-06-29 08:48:03,811][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 08:48:04,952][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-29 08:48:04,963][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-29 08:48:04,965][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-29 08:48:04,965][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-29 08:48:05,033][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-29 08:48:05,033][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 3512 ms 
[2025-06-29 08:48:05,670][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 08:48:06,066][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 08:48:06,360][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 08:48:09,090][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 08:48:10,422][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 08:48:10,831][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 08:48:12,073][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 08:48:12,164][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-29 08:48:12,174][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-29 08:48:12,228][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 11.405 seconds (process running for 12.026) 
[2025-06-29 08:48:12,659][INFO ][RMI TCP Connection(3)-192.168.50.194][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-29 08:48:12,659][INFO ][RMI TCP Connection(3)-192.168.50.194][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-29 08:48:12,661][INFO ][RMI TCP Connection(3)-192.168.50.194][o.s.w.s.DispatcherServlet:554] Completed initialization in 2 ms 
[2025-06-29 08:48:20,435][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-29 08:48:20,436][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-29 08:48:20,437][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:49:20,453][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:50:20,481][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:50:20,829][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 08:51:20,508][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:52:20,548][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:53:20,573][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:54:20,597][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:54:21,254][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 08:55:20,623][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:56:20,668][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:56:21,587][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 08:57:20,689][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:58:20,714][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 08:58:21,936][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->110ms 
[2025-06-29 08:59:20,744][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 09:00:20,776][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 09:00:22,273][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 17:06:25,700][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 17:06:25,731][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 20748 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-29 17:06:25,733][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-29 17:06:26,534][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 17:06:26,535][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 17:06:26,559][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
[2025-06-29 17:06:27,522][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 17:06:28,607][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-29 17:06:28,617][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-29 17:06:28,619][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-29 17:06:28,619][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-29 17:06:28,682][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-29 17:06:28,682][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 2917 ms 
[2025-06-29 17:06:29,281][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 17:06:29,656][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 17:06:29,941][INFO ][redisson-netty-2-7][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 17:06:30,615][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 17:06:31,674][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 17:06:32,054][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 17:06:32,967][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 17:06:33,024][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-29 17:06:33,032][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-29 17:06:33,065][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 7.815 seconds (process running for 8.838) 
[2025-06-29 17:06:33,455][INFO ][RMI TCP Connection(4)-192.168.50.194][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-29 17:06:33,455][INFO ][RMI TCP Connection(4)-192.168.50.194][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-29 17:06:33,457][INFO ][RMI TCP Connection(4)-192.168.50.194][o.s.w.s.DispatcherServlet:554] Completed initialization in 2 ms 
[2025-06-29 17:06:41,674][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-29 17:06:41,674][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-29 17:06:41,674][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:06:42,109][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 17:07:41,706][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:08:41,752][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:08:42,473][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 17:09:41,777][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:10:41,815][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:10:42,845][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->123ms 
[2025-06-29 17:11:41,841][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:12:41,864][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:13:41,897][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:14:41,943][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:14:43,351][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 17:15:10,217][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-29 17:15:41,965][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:16:41,997][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:16:43,720][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 17:17:42,028][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:18:42,066][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:18:44,112][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->110ms 
[2025-06-29 17:19:42,095][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:20:42,133][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:20:44,475][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 17:21:42,154][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:22:42,180][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:22:44,848][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 17:23:42,200][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:24:42,247][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:25:42,275][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:26:42,298][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:26:45,300][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->113ms 
[2025-06-29 17:27:55,237][INFO ][http-nio-1024-exec-4][o.s.a.AbstractOpenApiResource:390] Init duration for springdoc-openapi is: 685 ms 
[2025-06-29 17:28:05,374][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:28:45,683][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 17:29:05,408][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:30:05,460][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:30:46,044][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 17:31:05,484][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:32:05,505][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:32:46,404][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 17:33:24,735][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:34:24,770][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:34:46,794][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->124ms 
[2025-06-29 17:35:24,803][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:36:24,849][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:36:47,170][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 17:37:24,870][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:38:24,901][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:38:47,546][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 17:39:24,923][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:40:24,961][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:40:48,217][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->126ms 
[2025-06-29 17:41:24,990][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:42:25,016][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:42:48,590][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 17:43:25,038][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:44:25,078][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:44:48,963][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 17:45:25,106][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:46:25,151][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:46:49,336][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 17:47:25,178][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:48:25,212][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:48:49,724][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 17:49:25,233][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:50:25,282][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:50:50,074][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->112ms 
[2025-06-29 17:51:25,316][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:52:25,346][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:52:50,447][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 17:53:25,377][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:54:25,420][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:54:50,814][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 17:55:25,450][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:56:25,497][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:57:25,531][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:58:25,557][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 17:58:51,253][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 17:59:25,586][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:00:25,635][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:00:51,631][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 18:01:25,655][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:02:25,681][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:02:52,004][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 18:03:25,708][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:04:25,745][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:04:52,366][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->110ms 
[2025-06-29 18:05:25,779][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:06:25,828][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:06:52,722][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 18:07:25,853][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:08:25,878][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:08:53,098][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->121ms 
[2025-06-29 18:09:25,899][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:10:25,941][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:10:53,451][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->113ms 
[2025-06-29 18:11:25,972][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:12:26,004][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:12:53,827][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 18:13:26,029][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:14:26,069][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:15:10,215][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-29 18:15:26,102][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:16:26,143][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:16:54,296][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 18:17:26,166][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:18:26,186][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:18:54,681][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 18:19:26,217][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:20:26,262][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:20:55,045][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->113ms 
[2025-06-29 18:21:26,289][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:22:26,321][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:22:55,411][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->113ms 
[2025-06-29 18:23:26,342][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:24:26,382][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:24:55,783][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->121ms 
[2025-06-29 18:25:26,403][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:26:26,440][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:26:56,152][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 18:27:26,461][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:28:26,482][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:28:56,520][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 18:29:26,516][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:30:26,559][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:30:56,906][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 18:31:26,589][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:32:26,608][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:32:57,309][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 18:33:26,628][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:34:26,679][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:34:57,674][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 18:35:26,706][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:36:26,753][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:36:58,055][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 18:37:26,783][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:38:26,802][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:38:58,432][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->125ms 
[2025-06-29 18:39:26,866][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:40:26,907][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:40:58,802][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 18:41:26,935][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:42:26,959][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:43:26,984][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:44:27,024][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:44:59,237][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 18:45:27,048][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:46:27,093][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:46:59,627][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->126ms 
[2025-06-29 18:47:27,124][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:48:27,149][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:48:59,981][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 18:49:27,184][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:50:27,230][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:51:00,393][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->142ms 
[2025-06-29 18:51:27,251][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:52:27,277][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:53:00,749][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 18:53:27,296][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:54:27,334][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:55:01,116][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 18:55:27,355][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:56:27,396][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:57:01,489][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 18:57:27,415][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:58:27,439][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 18:59:01,862][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 18:59:27,468][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:00:27,544][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:01:27,635][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:02:27,660][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:03:02,498][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->242ms 
[2025-06-29 19:03:27,688][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:04:27,736][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:05:02,862][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 19:05:27,764][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:06:27,876][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:07:27,894][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:08:27,926][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:09:03,628][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->320ms 
[2025-06-29 19:09:27,946][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:10:27,991][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:11:04,008][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 19:11:28,022][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:12:28,054][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:13:04,372][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 19:13:28,085][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:14:28,156][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:15:04,796][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 19:15:10,223][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-29 19:15:28,247][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:16:28,286][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:17:05,161][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 19:17:28,319][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:18:28,351][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:19:05,548][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 19:19:28,382][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:20:28,430][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:21:05,929][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 19:21:28,453][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:22:28,502][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:23:06,358][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 19:23:28,535][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:24:28,569][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:25:06,810][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 19:25:28,602][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:26:28,645][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:27:28,741][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:28:28,765][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:29:07,342][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->121ms 
[2025-06-29 19:29:28,799][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:30:28,844][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:31:07,921][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->187ms 
[2025-06-29 19:31:28,875][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:32:28,902][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:33:08,371][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 19:33:28,988][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:34:29,034][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:35:09,034][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->254ms 
[2025-06-29 19:35:29,054][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:36:29,091][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:37:09,570][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->123ms 
[2025-06-29 19:37:29,120][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:38:29,150][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:39:10,085][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 19:39:29,234][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:40:29,270][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:41:10,600][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->197ms 
[2025-06-29 19:41:29,303][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:42:29,329][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:43:10,962][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 19:43:29,360][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:44:29,396][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:45:11,318][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->113ms 
[2025-06-29 19:45:29,482][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:46:29,524][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:47:11,780][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->120ms 
[2025-06-29 19:47:29,556][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:48:29,584][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:49:12,210][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 19:49:29,618][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:50:29,824][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:51:12,587][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 19:51:29,933][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:52:29,963][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:53:13,110][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 19:53:30,050][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:54:30,177][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:55:13,475][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 19:55:30,201][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:56:30,244][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:57:13,929][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 19:57:30,280][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:58:30,313][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 19:59:30,342][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:00:30,381][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:01:14,448][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 20:01:30,415][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:02:30,523][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:03:14,991][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->290ms 
[2025-06-29 20:03:30,632][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:04:30,678][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:05:15,367][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->121ms 
[2025-06-29 20:05:30,710][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:06:30,758][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:07:15,886][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->207ms 
[2025-06-29 20:07:30,790][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:08:30,813][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:09:30,839][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:10:30,876][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:11:16,515][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 20:11:30,895][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:12:30,927][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:13:17,053][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->206ms 
[2025-06-29 20:13:30,956][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:14:30,995][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:15:10,214][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-29 20:15:17,484][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->129ms 
[2025-06-29 20:15:31,027][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:16:31,076][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:17:17,931][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->123ms 
[2025-06-29 20:17:31,116][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:18:31,145][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:19:18,415][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 20:19:31,259][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:20:31,304][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:21:18,780][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 20:21:31,326][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:22:31,357][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:23:19,163][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->121ms 
[2025-06-29 20:23:31,387][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:24:31,428][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:25:19,524][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->113ms 
[2025-06-29 20:25:31,450][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:26:31,498][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:27:19,909][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 20:27:31,518][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:28:31,548][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:29:31,577][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:30:31,624][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:31:20,347][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 20:31:31,648][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:32:31,667][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:33:20,697][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 20:33:31,700][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:34:31,746][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:35:21,062][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 20:35:31,767][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:36:31,806][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:37:21,444][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 20:37:31,828][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:38:31,863][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:39:21,809][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 20:39:31,897][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:40:31,941][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:41:31,964][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:42:31,984][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:43:22,260][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 20:43:32,009][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:44:32,052][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:45:22,607][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->112ms 
[2025-06-29 20:45:32,083][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:46:32,128][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:47:22,972][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 20:47:32,149][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:48:32,181][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:49:23,338][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 20:49:32,200][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:50:32,235][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:51:23,724][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 20:51:32,257][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:52:32,281][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:53:24,087][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->119ms 
[2025-06-29 20:53:32,303][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:54:32,344][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:55:24,456][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 20:55:32,371][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:56:32,407][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:57:24,835][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 20:57:32,428][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:58:32,448][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 20:59:32,468][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:00:32,517][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:01:25,293][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 21:01:32,549][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:02:32,569][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:03:25,673][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->118ms 
[2025-06-29 21:03:32,597][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:04:32,645][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:05:26,061][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->116ms 
[2025-06-29 21:05:32,672][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:06:32,715][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:07:26,430][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 21:07:32,745][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:08:32,778][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:09:26,785][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->115ms 
[2025-06-29 21:09:32,795][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:10:32,841][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:11:27,161][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 21:11:32,860][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:12:32,900][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:13:27,526][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-29 21:13:32,923][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:17:17,888][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 21:17:17,932][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 29568 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-29 21:17:17,934][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-29 21:17:19,638][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 21:17:19,640][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 21:17:19,672][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces. 
[2025-06-29 21:17:20,760][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 21:17:21,802][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-29 21:17:21,812][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-29 21:17:21,813][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-29 21:17:21,814][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-29 21:17:21,877][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-29 21:17:21,877][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 3907 ms 
[2025-06-29 21:17:22,492][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 21:17:22,921][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 21:17:23,214][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 21:17:23,918][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-29 21:17:25,058][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 21:17:25,482][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 21:17:26,425][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 21:17:26,489][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-29 21:17:26,495][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-29 21:17:26,530][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 9.264 seconds (process running for 10.444) 
[2025-06-29 21:17:26,962][INFO ][RMI TCP Connection(3)-192.168.50.194][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-29 21:17:26,962][INFO ][RMI TCP Connection(3)-192.168.50.194][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-29 21:17:26,964][INFO ][RMI TCP Connection(3)-192.168.50.194][o.s.w.s.DispatcherServlet:554] Completed initialization in 2 ms 
[2025-06-29 21:17:35,048][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-29 21:17:35,049][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-29 21:17:35,049][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:17:35,477][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->112ms 
[2025-06-29 21:18:35,085][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:19:35,116][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:19:35,855][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 21:20:35,152][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:21:35,196][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:21:58,199][INFO ][http-nio-1024-exec-6][o.s.a.AbstractOpenApiResource:390] Init duration for springdoc-openapi is: 870 ms 
[2025-06-29 21:22:35,217][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:23:35,244][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:23:36,278][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->117ms 
[2025-06-29 21:24:35,261][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:25:35,299][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:25:36,625][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 21:26:35,331][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:27:35,352][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:27:36,962][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->107ms 
[2025-06-29 21:28:35,370][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:29:35,416][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:29:37,345][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->110ms 
[2025-06-29 21:30:35,446][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:31:35,481][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:31:37,688][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 21:32:35,501][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:33:35,527][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:33:38,023][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 21:34:35,546][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:35:35,588][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:35:38,350][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 21:36:35,607][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:37:35,623][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:37:38,673][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 21:38:35,655][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:39:35,691][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:39:39,020][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 21:40:35,715][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:41:35,758][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:41:39,363][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->107ms 
[2025-06-29 21:42:35,786][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:43:35,816][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:43:39,676][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 21:44:35,841][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:45:35,881][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:45:40,006][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 21:46:35,901][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:47:35,926][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:47:40,361][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 21:48:35,951][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:49:35,996][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:49:40,682][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 21:50:36,022][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:51:36,062][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:51:41,014][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 21:52:36,081][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:53:36,112][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:53:41,566][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->335ms 
[2025-06-29 21:54:36,130][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:55:36,171][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:55:41,888][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 21:56:36,194][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:57:36,220][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:58:36,246][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:59:36,290][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 21:59:42,266][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 22:00:36,322][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:01:36,361][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:01:42,608][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 22:02:36,389][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:03:36,413][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:03:42,956][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->124ms 
[2025-06-29 22:04:36,489][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:05:36,529][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:05:43,320][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-29 22:06:36,560][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:07:36,590][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:07:43,663][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 22:08:36,617][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:09:36,660][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:09:43,993][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 22:10:36,689][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:11:36,719][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:11:44,320][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 22:12:36,740][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:13:36,765][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:13:44,658][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 22:14:36,782][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:15:10,191][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-29 22:15:36,799][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:15:44,995][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-29 22:16:36,829][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:17:36,850][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:17:45,353][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 22:18:36,880][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:19:36,912][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:19:46,076][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->260ms 
[2025-06-29 22:20:36,940][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:21:36,975][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:21:46,446][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 22:22:36,996][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:23:37,024][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:23:46,773][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 22:24:37,052][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:25:37,084][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:26:37,103][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:27:37,128][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:27:47,167][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 22:28:37,152][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:29:37,189][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:29:47,490][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 22:30:37,206][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:31:37,240][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:31:47,828][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 22:32:37,268][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:33:37,300][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:34:37,327][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:35:37,365][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:35:48,233][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-29 22:36:37,388][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:37:37,420][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:37:48,562][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-29 22:38:37,450][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:39:37,491][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:39:48,896][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 22:40:37,520][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:41:37,551][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:42:37,580][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:43:37,597][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:43:49,297][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->109ms 
[2025-06-29 22:44:37,620][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:45:37,664][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:45:49,674][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->113ms 
[2025-06-29 22:46:37,688][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:47:37,715][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:47:50,013][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 22:48:37,742][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:49:37,784][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:49:50,350][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 22:50:37,804][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:51:37,836][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:51:50,677][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 22:52:37,866][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:53:37,896][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:53:51,003][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-29 22:54:37,924][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:55:37,958][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:55:51,350][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-29 22:56:37,986][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:57:38,014][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:57:51,703][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 22:58:38,036][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:59:38,082][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 22:59:52,048][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 23:00:38,105][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:01:38,140][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:01:52,374][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 23:02:38,159][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:03:38,190][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:03:52,720][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 23:04:38,223][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:05:38,265][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:05:53,080][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 23:06:38,287][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:07:38,317][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:07:53,410][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-29 23:08:38,337][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:09:38,370][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:09:53,750][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-29 23:10:38,396][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:11:38,443][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:11:54,084][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 23:12:38,463][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:13:38,479][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:13:54,432][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 23:14:38,500][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:15:10,201][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-29 23:15:38,526][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:15:54,792][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->111ms 
[2025-06-29 23:16:38,560][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:17:38,587][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:18:38,612][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:19:38,651][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:19:55,183][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-29 23:20:38,674][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:21:38,706][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:21:55,508][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 23:22:38,724][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:23:38,747][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:23:56,081][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->328ms 
[2025-06-29 23:24:38,763][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:25:38,810][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:25:56,422][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-29 23:26:38,827][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:27:38,855][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:27:56,744][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 23:28:38,881][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:29:38,920][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:29:57,067][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 23:30:38,953][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:31:38,989][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:31:57,392][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-29 23:32:39,018][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:33:39,037][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:33:57,734][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 23:34:39,062][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:35:39,110][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:35:58,053][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 23:36:39,129][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:37:39,158][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:37:58,386][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 23:38:39,186][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:39:39,230][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:39:58,698][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-29 23:40:39,254][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:41:39,294][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:41:59,058][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-29 23:42:39,320][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:43:39,353][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:43:59,386][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-29 23:44:39,373][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:45:39,405][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:45:59,719][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-29 23:46:39,420][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:47:39,444][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:48:00,052][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->112ms 
[2025-06-29 23:48:39,468][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:49:39,502][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:50:00,381][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-29 23:50:39,537][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:51:39,577][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:52:00,926][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 23:52:39,602][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:53:39,630][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:54:01,263][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 23:54:39,658][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:55:39,692][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:56:01,576][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-29 23:56:39,720][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:57:39,742][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:58:01,901][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-29 23:58:39,767][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-29 23:59:39,810][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
