spring:
  # 数据库连接信息
  datasource:
    url: *****************************************************************************************************************************************************************************************************************
    username: root
    password: SmartAdmin666
    driver-class-name: com.mysql.cj.jdbc.Driver
    initial-size: 10
    min-idle: 10
    max-active: 100
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    filters: stat
    druid:
      username: druid
      password: 1024lab
      login:
        enabled: false
      method:
        pointcut: net.lab1024.sa..*Service.*

  # redis 连接池配置信息
  data:
    redis:
      database: 1
      host: 127.0.0.1
      port: 6379
      password:
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 100
          min-idle: 10
          max-idle: 50
          max-wait: 30000ms

  # 邮件，置以SSL的方式发送, 这个需要使用这种方式并且端口是465
  mail:
    host: smtp.163.com
    port: 465
    username: <EMAIL>
    password: LAB1024LAB
    test-connection: false
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
            socketFactory:
              class: com.sun.mail.util.MailSSLSocketFactory
              fallback: false
        debug: false

  # json序列化相关配置
  jackson:
    serialization:
      write-enums-using-to-string: true
      write-dates-as-timestamps: false
    deserialization:
      read-enums-using-to-string: true
      fail-on-unknown-properties: false
    default-property-inclusion: always
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  # 缓存实现类型
  cache:
    type: redis

# tomcat 配置，主要用于 配置 访问日志（便于将来排查错误）
server:
  tomcat:
    basedir: ${project.log-directory}/tomcat-logs
    accesslog:
      enabled: true
      max-days: 30
      pattern: "%t %{X-Forwarded-For}i %a %r %s (%D ms) %I (%B byte)"


# 文件上传 配置
file:
  storage:
    mode: local
    local:
      upload-path: /home/<USER>/upload/   #文件上传目录
      url-prefix:
    cloud:
      region: oss-cn-hangzhou
      endpoint: oss-cn-hangzhou.aliyuncs.com
      bucket-name: 1024lab-smart-admin
      access-key:
      secret-key:
      url-prefix: https://${file.storage.cloud.bucket-name}.${file.storage.cloud.endpoint}/
      private-url-expire-seconds: 3600


# open api配置
springdoc:
  swagger-ui:
    enabled: true # 开关
    doc-expansion: none  #关闭展开
    tags-sorter: alpha
    server-base-url: https://preview.smartadmin.vip/smart-admin-api
  api-docs:
    enabled: true # 开关
knife4j:
  enable: true
  basic:
    enable: false
    username: api # Basic认证用户名
    password: 1024 # Basic认证密码

# RestTemplate 请求配置
http:
  pool:
    max-total: 100
    connect-timeout: 50000
    read-timeout: 50000
    write-timeout: 50000
    keep-alive: 300000

# 心跳配置
heart-beat:
  interval-seconds: 60

# 热加载配置
reload:
  interval-seconds: 60

# sa-token 配置
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 前缀 例如:Bear
  token-prefix: Bearer
  # token 有效期（单位：秒） 默认30天（2592000秒），-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）(jwt模式下恒false)
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）(jwt模式下无用)
  token-style: simple-uuid
  # 是否打开自动续签 （如果此值为true，框架会在每次直接或间接调用 getLoginId() 时进行一次过期检查与续签操作）
  auto-renew: true
  # 是否输出操作日志
  is-log: false
  # 日志等级（trace、debug、info、warn、error、fatal）
  log-level: warn
  # 启动时的字符画打印
  is-print: false
  # 是否从cookie读取token
  is-read-cookie: false

# SmartJob 定时任务配置(不需要可以直接删除以下配置，详细文档请看：https://www.xxxxxx.com)
smart:
  job:
    enabled: true
    # 任务初始化延迟 默认30秒 可选
    init-delay: 10
    # 定时任务执行线程池数量 默认2 可选
    core-pool-size: 2
    # 数据库配置检测-开关 默认开启 可选（作用是固定间隔读取数据库配置更新任务，关闭后只能重启服务或通过接口修改定时任务，建议开启）
    db-refresh-enabled: true
    # 数据库配置检测-执行间隔 默认120秒 可选
    db-refresh-interval: 60