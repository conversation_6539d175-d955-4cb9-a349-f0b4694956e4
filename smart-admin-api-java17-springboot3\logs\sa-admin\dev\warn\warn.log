[2025-06-30 01:48:57,783][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:57,790][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:57,793][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:57,798][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:57,800][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:57,801][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,223][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,586][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-30 01:48:58,764][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,767][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,773][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,776][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,779][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,784][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,787][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,790][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,793][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,800][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,801][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:48:58,804][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 01:49:02,457][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-30 01:49:02,557][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://**************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-30 04:09:49,994][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:49,999][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:50,002][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:50,006][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:50,009][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:50,011][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:50,384][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:50,778][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-30 04:09:51,017][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,022][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,029][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,033][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,037][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,043][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,047][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,049][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,052][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,057][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,059][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:51,062][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:09:54,802][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-30 04:09:54,915][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-30 04:45:01,531][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:01,536][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:01,539][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:01,544][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:01,546][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:01,549][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:01,951][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,370][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-30 04:45:02,727][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,729][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,738][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,741][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,745][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,751][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,755][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,757][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,759][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,765][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,767][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:02,771][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:45:06,599][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-30 04:45:06,710][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-30 04:56:53,630][WARN ][SpringApplicationShutdownHook][o.s.s.c.ThreadPoolTaskScheduler:366] Timed out while waiting for executor to terminate 
[2025-06-30 04:57:42,547][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:42,552][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:42,555][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:42,559][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:42,560][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:42,563][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:42,996][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,574][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-30 04:57:43,828][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,830][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,838][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,841][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,845][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,851][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,855][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,857][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,861][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,865][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,867][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:43,871][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 04:57:48,000][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-30 04:57:48,111][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-30 05:11:42,532][WARN ][SpringApplicationShutdownHook][o.s.s.c.ThreadPoolTaskScheduler:366] Timed out while waiting for executor to terminate 
[2025-06-30 05:11:48,929][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:48,934][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:48,938][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:48,943][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:48,945][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:48,948][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:49,351][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:49,734][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-30 05:11:50,136][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,139][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,146][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,149][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,154][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,159][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,163][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,164][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,168][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,173][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,175][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:50,178][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 05:11:53,962][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-30 05:11:54,067][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-30 06:33:54,812][WARN ][SpringApplicationShutdownHook][o.s.s.c.ThreadPoolTaskScheduler:366] Timed out while waiting for executor to terminate 
[2025-06-30 06:42:49,922][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:49,928][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:49,933][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:49,938][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:49,941][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:49,942][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:50,364][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:50,823][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-30 06:42:51,111][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,114][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,126][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,129][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,135][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,143][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,148][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,151][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,153][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,162][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,163][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy133] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:51,168][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 06:42:55,109][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-30 06:42:55,225][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-30 07:39:24,554][WARN ][SpringApplicationShutdownHook][o.s.s.c.ThreadPoolTaskScheduler:366] Timed out while waiting for executor to terminate 
[2025-06-30 07:50:19,991][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:19,997][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:20,002][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:20,007][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:20,010][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:20,012][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:20,444][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:20,967][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-30 07:50:21,290][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,294][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,302][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,306][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,314][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,322][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,334][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,338][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,342][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,349][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,351][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy133] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:21,355][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-30 07:50:25,411][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-30 07:50:25,539][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-30 08:34:19,428][WARN ][SpringApplicationShutdownHook][o.s.s.c.ThreadPoolTaskScheduler:366] Timed out while waiting for executor to terminate 
