# CardInfo 消息类型修复说明

## 问题描述

原始错误信息：
```
Could not resolve type id 'net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload' as a subtype of `java.lang.Object`: no such class found
```

这个错误发生在WebSocket消息反序列化过程中，Jackson无法找到`CardInfoPayload`类。

## 问题原因

1. 在`MessageTypeEnum`中定义了`CARD_INFO(14, "CARD_INFO")`消息类型
2. 但是缺少对应的`CardInfoPayload`类
3. 在`NfcRelayEndpoint.onMessage()`方法中没有处理`CARD_INFO`消息类型

## 解决方案

### 1. 创建了 CardInfoPayload 类

文件：`sa-admin/src/main/java/net/lab1024/sa/admin/module/business/nfc/ws/domain/CardInfoPayload.java`

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CardInfoPayload {
    private String cardId;           // 卡片唯一标识符
    private String cardType;         // 卡片类型
    private String technology;       // 卡片技术类型
    private String size;             // 卡片大小/容量信息
    private String manufacturer;     // 卡片制造商信息
    private Object additionalData;   // 附加的卡片数据或属性
    
    // 便利构造器...
}
```

### 2. 在 NfcRelayEndpoint 中添加了 CARD_INFO 处理

在`onMessage()`方法的switch语句中添加：
```java
case CARD_INFO:
    handleCardInfo(session, employeeId, webSocketMessage.getPayload());
    break;
```

并实现了`handleCardInfo()`方法：
- 解析CardInfoPayload
- 验证数据有效性
- 转发给伙伴设备
- 发送确认消息

### 3. 添加了相应的测试

在`NfcRelayEndpointIT.java`中添加了两个测试方法：
- `testCardInfoRelay()`: 测试正常的卡片信息中继
- `testCardInfoWithInvalidPayload()`: 测试无效载荷的错误处理

## 修复效果

修复后，当客户端发送包含`CARD_INFO`类型的WebSocket消息时：

1. Jackson能够成功反序列化`CardInfoPayload`
2. 服务器能够正确处理`CARD_INFO`消息
3. 卡片信息会被转发给配对的伙伴设备
4. 发送方会收到确认消息

## 使用示例

客户端发送卡片信息：
```json
{
  "type": "CARD_INFO",
  "payload": {
    "@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload",
    "card_id": "04:12:34:56:78:90:AB",
    "card_type": "MIFARE_CLASSIC",
    "technology": "NfcA",
    "size": "1K",
    "manufacturer": "NXP"
  }
}
```

服务器处理流程：
1. 反序列化为`CardInfoPayload`对象
2. 验证`cardId`不为空
3. 转发给伙伴设备
4. 发送确认消息给发送方

这样就完全解决了原始的Jackson反序列化错误。
