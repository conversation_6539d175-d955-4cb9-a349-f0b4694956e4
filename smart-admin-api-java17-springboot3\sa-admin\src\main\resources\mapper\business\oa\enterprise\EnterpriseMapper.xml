<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.oa.enterprise.dao.EnterpriseDao">
    <update id="deleteEnterprise">
        UPDATE t_oa_enterprise
        SET deleted_flag = #{deletedFlag}
        WHERE enterprise_id = #{enterpriseId}
    </update>
    <select id="queryByEnterpriseName"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.entity.EnterpriseEntity">
        SELECT *
        FROM t_oa_enterprise
        WHERE enterprise_name = #{enterpriseName}
        AND deleted_flag = #{deletedFlag}
        <if test="excludeEnterpriseId != null">
            AND enterprise_id != #{excludeEnterpriseId}
        </if>
    </select>

    <select id="queryPage"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.vo.EnterpriseVO">
        SELECT t_oa_enterprise.*
        FROM t_oa_enterprise
        <where>
            deleted_flag = #{queryForm.deletedFlag}
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (INSTR(enterprise_name,#{queryForm.keywords}) OR INSTR(contact,#{queryForm.keywords}) OR
                INSTR(contact_phone,#{queryForm.keywords}) OR INSTR(create_user_name,#{queryForm.keywords}))
            </if>
            <if test="queryForm.startTime != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &gt;= #{queryForm.startTime}
            </if>
            <if test="queryForm.endTime != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &lt;= #{queryForm.endTime}
            </if>
            <if test="queryForm.disabledFlag != null">
                AND disabled_flag = #{queryForm.disabledFlag}
            </if>
        </where>
        <if test="queryForm.sortItemList == null or queryForm.sortItemList.size == 0">
            ORDER BY create_time DESC
        </if>
    </select>

    <select id="selectExcelExportData"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.vo.EnterpriseExcelVO">
        SELECT t_oa_enterprise.*
        FROM t_oa_enterprise
        <where>
            deleted_flag = #{queryForm.deletedFlag}
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (INSTR(enterprise_name,#{queryForm.keywords}) OR INSTR(contact,#{queryForm.keywords}) OR
                INSTR(contact_phone,#{queryForm.keywords}) OR INSTR(create_user_name,#{queryForm.keywords}))
            </if>
            <if test="queryForm.startTime != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &gt;= #{queryForm.startTime}
            </if>
            <if test="queryForm.endTime != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &lt;= #{queryForm.endTime}
            </if>
            <if test="queryForm.disabledFlag != null">
                AND disabled_flag = #{queryForm.disabledFlag}
            </if>
        </where>
        <if test="queryForm.sortItemList == null or queryForm.sortItemList.size == 0">
            ORDER BY create_time DESC
        </if>
    </select>



    <select id="queryList"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.vo.EnterpriseListVO">
        SELECT enterprise_id, enterprise_name
        FROM t_oa_enterprise
        WHERE disabled_flag = #{disabledFlag}
        AND deleted_flag = #{deletedFlag}
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>
    <select id="getDetail"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.vo.EnterpriseVO">
        SELECT oe.*
        FROM t_oa_enterprise oe
        WHERE oe.enterprise_id = #{enterpriseId} AND oe.deleted_flag = #{deletedFlag}
    </select>

</mapper>