<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- WebSocket专用日志文件 -->
    <appender name="WEBSOCKET_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/websocket.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/websocket.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- APDU专用日志文件 -->
    <appender name="APDU_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/apdu.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/apdu.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
        <!-- 只记录包含APDU的日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("APDU")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- WebSocket相关的logger配置 -->
    <logger name="net.lab1024.sa.admin.module.business.nfc.ws.NfcRelayEndpoint" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="WEBSOCKET_FILE"/>
        <appender-ref ref="APDU_FILE"/>
    </logger>

    <!-- NFC会话管理器的日志 -->
    <logger name="net.lab1024.sa.admin.module.business.nfc.ws.NfcSessionManager" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="WEBSOCKET_FILE"/>
    </logger>

    <!-- 根logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
