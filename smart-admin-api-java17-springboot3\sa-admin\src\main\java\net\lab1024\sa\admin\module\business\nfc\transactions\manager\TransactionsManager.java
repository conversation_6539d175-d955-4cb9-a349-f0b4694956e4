package net.lab1024.sa.admin.module.business.nfc.transactions.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.nfc.transactions.dao.TransactionsDao;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.entity.TransactionsEntity;
import org.springframework.stereotype.Service;

/**
 * NFC中继交易记录表 Manager
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:41:36
 * @Copyright xueh所有
 */
@Service
public class TransactionsManager extends ServiceImpl<TransactionsDao, TransactionsEntity> {
}
