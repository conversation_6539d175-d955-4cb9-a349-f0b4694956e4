package net.lab1024.sa.admin.module.business.nfc.transactions.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.form.TransactionsQueryForm;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.vo.TransactionsVO;
import net.lab1024.sa.admin.module.business.nfc.transactions.service.TransactionsService;
import net.lab1024.sa.base.common.controller.SupportBaseController;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * NFC中继交易记录表 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:41:36
 * @Copyright xueh所有
 */
@RestController
@Tag(name = AdminSwaggerTagConst.Business.NFC_TRANSACTIONS)
public class TransactionsController extends SupportBaseController {

    @Resource
    private TransactionsService transactionsService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/transactions/queryPage")
    public ResponseDTO<PageResult<TransactionsVO>> queryPage(@RequestBody TransactionsQueryForm queryForm) {
        return ResponseDTO.ok(transactionsService.queryPage(queryForm));
    }

}
