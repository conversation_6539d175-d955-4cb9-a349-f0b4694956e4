[2025-06-29 22:39:10,879][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:10,890][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:10,898][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:10,905][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:10,910][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:10,913][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:11,571][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,623][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 22:39:12,902][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,905][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,926][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,937][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,946][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,959][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,968][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,973][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,981][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy140] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,990][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,993][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy141] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:12,996][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:39:19,188][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 22:50:04,837][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:04,847][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:04,851][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:04,857][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:04,861][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:04,862][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:05,361][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,501][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 22:50:06,810][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,814][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,839][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,884][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,902][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,911][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,920][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,923][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,932][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy140] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,940][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,944][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy141] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:06,947][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:50:12,853][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
