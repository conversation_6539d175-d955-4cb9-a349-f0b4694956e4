[2025-06-29 22:39:08,507][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:39:08,555][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 37196 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:39:08,567][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "dev" 
[2025-06-29 22:39:09,759][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:39:09,762][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:39:09,792][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:39:11,568][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:39:13,882][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:39:14,282][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 22:39:14,532][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for ************/************:6379 
[2025-06-29 22:39:15,208][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for ************/************:6379 
[2025-06-29 22:39:17,157][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 22:39:17,858][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 22:39:19,233][INFO ][main][o.s.b.t.m.w.SpringBootMockServletContext:437] Initializing Spring TestDispatcherServlet '' 
[2025-06-29 22:39:19,233][INFO ][main][o.s.t.w.s.TestDispatcherServlet:532] Initializing Servlet '' 
[2025-06-29 22:39:19,240][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 22:39:19,252][INFO ][main][o.s.t.w.s.TestDispatcherServlet:554] Completed initialization in 17 ms 
[2025-06-29 22:39:19,344][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:56] Started UserRegisterControllerTests in 11.265 seconds (process running for 12.145) 
[2025-06-29 22:39:20,125][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-29 22:39:20,154][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:39:20,158][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-29 22:50:02,658][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:50:02,710][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 31384 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:50:02,716][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "dev" 
[2025-06-29 22:50:03,926][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:50:03,928][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:50:03,950][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:50:05,359][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:50:07,776][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:50:08,167][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 22:50:08,423][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for ************/************:6379 
[2025-06-29 22:50:09,102][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for ************/************:6379 
[2025-06-29 22:50:10,655][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 22:50:11,402][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 22:50:12,917][INFO ][main][o.s.b.t.m.w.SpringBootMockServletContext:437] Initializing Spring TestDispatcherServlet '' 
[2025-06-29 22:50:12,917][INFO ][main][o.s.t.w.s.TestDispatcherServlet:532] Initializing Servlet '' 
[2025-06-29 22:50:12,924][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 22:50:12,936][INFO ][main][o.s.t.w.s.TestDispatcherServlet:554] Completed initialization in 19 ms 
[2025-06-29 22:50:13,058][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:56] Started UserRegisterControllerTests in 10.836 seconds (process running for 11.744) 
[2025-06-29 22:50:14,117][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-29 22:50:14,149][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:50:14,151][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
