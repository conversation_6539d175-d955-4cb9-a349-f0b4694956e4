package net.lab1024.sa.admin.module.business.nfc.ws;

import net.lab1024.sa.admin.module.system.login.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * NFC WebSocket 端点注入器
 */
@Component
public class NfcEndpointInjector {

    @Autowired
    private LoginService loginService;

    @PostConstruct
    public void init() {
        // 此方法留空，因为原始代码不包含任何初始化逻辑
    }
} 