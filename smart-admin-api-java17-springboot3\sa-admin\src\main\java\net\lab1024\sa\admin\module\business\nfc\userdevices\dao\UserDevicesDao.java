package net.lab1024.sa.admin.module.business.nfc.userdevices.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.nfc.userdevices.domain.entity.UserDevicesEntity;
import net.lab1024.sa.admin.module.business.nfc.userdevices.domain.form.UserDevicesQueryForm;
import net.lab1024.sa.admin.module.business.nfc.userdevices.domain.vo.UserDevicesVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 用户登录设备表 Dao
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:47:54
 * @Copyright xueh所有
 */

@Mapper
public interface UserDevicesDao extends BaseMapper<UserDevicesEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<UserDevicesVO> queryPage(Page page, @Param("queryForm") UserDevicesQueryForm queryForm);

}
