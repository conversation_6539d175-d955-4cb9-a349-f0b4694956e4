package net.lab1024.sa.admin.module.business.nfc.ws;

import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.entity.TransactionsEntity;
import net.lab1024.sa.admin.module.business.nfc.transactions.service.TransactionsService;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.*;
import net.lab1024.sa.admin.module.system.login.domain.RequestEmployee;
import net.lab1024.sa.admin.module.system.login.manager.LoginManager;
import net.lab1024.sa.admin.module.system.login.service.LoginService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import net.lab1024.sa.base.common.util.SmartEnumUtil;
import org.springframework.stereotype.Component;

import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;

/**
 * NFC Relay WebSocket Endpoint
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/ws/nfc/{token}")
public class NfcRelayEndpoint {

    /**
     * 用于保存注入的 Spring bean 的静态字段。
     * 这是将依赖项注入 @ServerEndpoint 实例的标准解决方法，
     * 不由 Spring 的容器直接管理。
     */
    private static LoginService loginService;
    private static LoginManager loginManager;
    private static NfcSessionManager nfcSessionManager;
    private static TransactionsService transactionsService;

    /**
     * 将 Spring 管理的 bean 注入 static 字段的方法。
     */
    public static void autowire(LoginService loginService, LoginManager loginManager, NfcSessionManager nfcSessionManager, TransactionsService transactionsService) {
        NfcRelayEndpoint.loginService = loginService;
        NfcRelayEndpoint.loginManager = loginManager;
        NfcRelayEndpoint.nfcSessionManager = nfcSessionManager;
        NfcRelayEndpoint.transactionsService = transactionsService;
    }

    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        log.info("WebSocket onOpen: session={}, token={}", session.getId(), token);

        try {
            // Sa-Token WebSocket 最佳实践：直接验证URL中的token
            String loginId = (String) StpUtil.getLoginIdByToken(token);
            
            if (loginId == null) {
                log.warn("WebSocket onOpen failed: Invalid token. session={}, token={}", session.getId(), token);
                sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_TOKEN", "无效的凭证，请重新登录"));
                closeSession(session, "Invalid token");
                return;
            }

            // 获取员工ID
            Long employeeId = loginService.getEmployeeIdByLoginId(loginId);
            if (employeeId == null) {
                log.warn("WebSocket onOpen failed: Cannot parse employeeId from loginId={}. session={}, token={}", 
                         loginId, session.getId(), token);
                sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_LOGIN_ID", "无效的用户标识"));
                closeSession(session, "Invalid loginId");
                return;
            }

            // 获取用户完整信息
            RequestEmployee requestEmployee = loginManager.getRequestEmployee(employeeId);
            if (requestEmployee == null) {
                log.warn("WebSocket onOpen failed: Cannot get RequestEmployee for employeeId={}. session={}, token={}", 
                         employeeId, session.getId(), token);
                sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("USER_NOT_FOUND", "无法获取用户信息"));
                closeSession(session, "User not found");
                return;
            }

            // 在会话属性中存储用户信息
            session.getUserProperties().put("employeeId", requestEmployee.getEmployeeId());
            session.getUserProperties().put("employeeName", requestEmployee.getActualName());

            // 获取登录设备信息（模仿AdminInterceptor的处理方式）
            try {
                Object loginDevice = StpUtil.getSessionByLoginId(loginId).get("loginDevice");
                if (loginDevice != null) {
                    session.getUserProperties().put("loginDevice", loginDevice);
                    log.debug("WebSocket onOpen: Set loginDevice={} for employeeId={}, session={}", 
                             loginDevice, requestEmployee.getEmployeeId(), session.getId());
                }
            } catch (Exception e) {
                log.warn("WebSocket onOpen: Failed to get loginDevice for employeeId={}, session={}, error={}", 
                         requestEmployee.getEmployeeId(), session.getId(), e.getMessage());
                // loginDevice获取失败不影响主要功能，继续执行
            }

            log.info("WebSocket onOpen success: employeeId={}, employeeName='{}', session={}", 
                     requestEmployee.getEmployeeId(), requestEmployee.getActualName(), session.getId());
            sendMessage(session, MessageTypeEnum.STATUS_UPDATE, new StatusUpdatePayload("CONNECTED", "连接认证成功"));
            
        } catch (Exception e) {
            log.warn("WebSocket onOpen failed: Token validation exception. session={}, token={}, error={}", 
                     session.getId(), token, e.getMessage());
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_TOKEN", "无效的凭证，请重新登录"));
            closeSession(session, "Token validation failed: " + e.getMessage());
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        Long employeeId = (Long) session.getUserProperties().get("employeeId");
        log.debug("WebSocket onMessage 的: employeeId={}, session={}, message={}", employeeId, session.getId(), message);

        try {
            WebSocketMessage<?> webSocketMessage = new ObjectMapper().readValue(message, WebSocketMessage.class);
            MessageTypeEnum messageType = webSocketMessage.getType();

            if (messageType == null) {
                log.warn("收到未知消息类型.");
                return;
            }

            switch (messageType) {
                case REGISTER_ROLE:
                    handleRegisterRole(session, employeeId, webSocketMessage.getPayload());
                    break;
                case APDU_COMMAND:
                case APDU_RESPONSE:
                    handleApduRelay(session, employeeId, webSocketMessage);
                    break;
                case CLIENT_STATE_UPDATE:
                    handleClientStateUpdate(session, employeeId, webSocketMessage.getPayload());
                    break;
                case SESSION_END:
                    handleSessionEnd(session, employeeId, webSocketMessage.getPayload());
                    break;
                case CARD_INFO:
                    handleCardInfo(session, employeeId, webSocketMessage.getPayload());
                    break;
                // 其他情况将在稍后添加
                default:
                    log.warn("Unhandled message type: {}", messageType);
            }
        } catch (Exception e) {
            log.error("WebSocket onMessage error: " + e.getMessage(), e);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_MESSAGE_FORMAT", "无效的消息格式"));
        }
    }

    private void handleRegisterRole(Session session, Long employeeId, Object payload) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

        try {
            RoleRegisterPayload rolePayload = objectMapper.convertValue(payload, RoleRegisterPayload.class);
            NfcSessionManager.Role role = NfcSessionManager.Role.valueOf(rolePayload.getRole().toUpperCase());

            NfcSessionManager.PairingResult result = nfcSessionManager.register(employeeId, role, session);

            if (result.isPaired()) {
                log.info("Pairing successful for employeeId: {}", employeeId);
                
                // Create a new transaction record in the database
                TransactionsEntity transaction = transactionsService.createTransaction(employeeId);
                nfcSessionManager.setTransactionId(employeeId, transaction.getId());

                sendMessage(session, MessageTypeEnum.PAIRED, null);
                sendMessage(result.partnerSession(), MessageTypeEnum.PAIRED, null);
            } else {
                log.info("Waiting for partner for employeeId: {}", employeeId);
                sendMessage(session, MessageTypeEnum.STATUS_UPDATE, new StatusUpdatePayload("WAITING_FOR_PARTNER", "已就绪，正在等待您的另一台设备..."));
            }
        } catch (SessionAlreadyActiveException e) {
            log.warn("Registration rejected for userId {}: {}", employeeId, e.getMessage());
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("SESSION_ALREADY_ACTIVE", "连接失败：您的账号已在其他设备上建立中继连接。"));
            closeSession(session, "Session already active");
        } catch (IllegalArgumentException e) {
            log.warn("Invalid role received for employeeId: {}. Payload: {}", employeeId, payload);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_ROLE", "无效的角色类型"));
        } catch (Exception e) {
            log.error("Error handling REGISTER_ROLE for employeeId: {}", employeeId, e);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("REGISTRATION_FAILED", "角色注册失败"));
        }
    }

    private void handleApduRelay(Session session, Long employeeId, WebSocketMessage message) {
        Session partnerSession = nfcSessionManager.getPartnerSession(session);

        if (partnerSession != null && partnerSession.isOpen()) {
            // Forward the original message to the partner
            sendMessage(partnerSession, message.getType(), message.getPayload());
            log.debug("Relayed APDU for employeeId: {} from {} to {}", employeeId, session.getId(), partnerSession.getId());
        } else {
            log.warn("Cannot relay APDU. Partner session not found or closed for employeeId: {}", employeeId);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("PARTNER_UNAVAILABLE", "伙伴设备已离线，无法转发数据"));
        }
    }

    private void handleClientStateUpdate(Session session, Long employeeId, Object payload) {
        log.debug("Handling CLIENT_STATE_UPDATE for employeeId: {}", employeeId);
        
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        
        try {
            // 1. Parse the payload
            ClientStateUpdatePayload statePayload = objectMapper.convertValue(payload, ClientStateUpdatePayload.class);
            if (statePayload == null || statePayload.getState() == null) {
                throw new IllegalArgumentException("ClientStateUpdatePayload or state is null");
            }
            
            log.info("Received state update from employeeId {}: {}", employeeId, statePayload.getState());
            
            // 2. Find the partner session
            Session partnerSession = nfcSessionManager.getPartnerSession(session);
            if (partnerSession == null || !partnerSession.isOpen()) {
                log.warn("Cannot forward state update. Partner session not found or closed for employeeId: {}", employeeId);
                sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("PARTNER_UNAVAILABLE", "伙伴设备已离线，无法同步状态"));
                return;
            }
            
            // 3. Convert client state to partner notification status
            String partnerStatus = convertToPartnerStatus(statePayload.getState());
            if (partnerStatus == null) {
                log.warn("Unknown client state received: {} from employeeId: {}", statePayload.getState(), employeeId);
                sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_STATE", "无效的状态类型"));
                return;
            }
            
            // 4. Send status update to partner
            String partnerMessage = getPartnerMessage(statePayload.getState());
            sendMessage(partnerSession, MessageTypeEnum.STATUS_UPDATE, new StatusUpdatePayload(partnerStatus, partnerMessage));
            log.info("Forwarded state update to partner. employeeId: {}, partnerStatus: {}", employeeId, partnerStatus);
            
        } catch (Exception e) {
            log.warn("Invalid CLIENT_STATE_UPDATE payload for employeeId: {}. Payload: {}", employeeId, payload, e);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_PAYLOAD", "无效的状态更新参数"));
        }
    }
    
    /**
     * 将客户端状态转换为对应的伙伴通知状态
     */
    private String convertToPartnerStatus(String clientState) {
        switch (clientState.toUpperCase()) {
            case "CARD_ATTACHED":
                return "PARTNER_CARD_ATTACHED";
            case "CARD_DETACHED":
                return "PARTNER_CARD_DETACHED";
            default:
                return null;
        }
    }
    
    /**
     * 根据客户端状态生成对应的伙伴提示消息
     */
    private String getPartnerMessage(String clientState) {
        switch (clientState.toUpperCase()) {
            case "CARD_ATTACHED":
                return "对方设备已准备就绪，请开始操作。";
            case "CARD_DETACHED":
                return "对方设备卡片已断开。";
            default:
                return "对方设备状态已更新。";
        }
    }

    private void handleSessionEnd(Session session, Long employeeId, Object payload) {
        log.info("Handling SESSION_END for employeeId: {}", employeeId);
        Session partnerSession = nfcSessionManager.getPartnerSession(session);
        Long transactionId = nfcSessionManager.getTransactionId(employeeId);

        // 1. Parse the payload
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        SessionEndPayload sessionEndPayload;
        try {
            sessionEndPayload = objectMapper.convertValue(payload, SessionEndPayload.class);
            if (sessionEndPayload == null || sessionEndPayload.getStatus() == null) {
                throw new IllegalArgumentException("SessionEndPayload or status is null");
            }
        } catch (Exception e) {
            log.warn("Invalid SESSION_END payload for employeeId: {}. Payload: {}", employeeId, payload, e);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_PAYLOAD", "无效的会话结束参数"));
            return;
        }

        // 2. Update transaction in database
        if (transactionId != null) {
            log.info("Updating transaction {} for employeeId {} with status: {}", transactionId, employeeId, sessionEndPayload.getStatus());
            transactionsService.updateTransactionAsFinished(transactionId, sessionEndPayload.getStatus(), sessionEndPayload.getReason());
        } else {
            log.warn("Could not find transactionId for employeeId {} to update status.", employeeId);
        }

        // 3. Notify the partner device
        if (partnerSession != null && partnerSession.isOpen()) {
            log.info("Notifying partner {} about SESSION_END for employeeId: {}", partnerSession.getId(), employeeId);
            sendMessage(partnerSession, MessageTypeEnum.SESSION_END, sessionEndPayload);
        }

        // 4. Clean up and close both sessions
        log.info("Closing sessions for employeeId: {}", employeeId);
        nfcSessionManager.removeSession(session); // Remove the reporting session
        if (partnerSession != null) {
            nfcSessionManager.removeSession(partnerSession); // Remove the partner session
            closeSession(partnerSession, "Session ended by partner");
        }
        closeSession(session, "Session ended by client");
    }

    private void handleCardInfo(Session session, Long employeeId, Object payload) {
        log.info("Handling CARD_INFO for employeeId: {}", employeeId);

        // 1. Parse the payload
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        CardInfoPayload cardInfoPayload;
        try {
            cardInfoPayload = objectMapper.convertValue(payload, CardInfoPayload.class);
            if (cardInfoPayload == null || cardInfoPayload.getCardId() == null) {
                throw new IllegalArgumentException("CardInfoPayload or cardId is null");
            }
        } catch (Exception e) {
            log.warn("Invalid CARD_INFO payload for employeeId: {}. Payload: {}", employeeId, payload, e);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("INVALID_PAYLOAD", "无效的卡片信息参数"));
            return;
        }

        log.info("Received card info from employeeId {}: cardId={}, cardType={}, technology={}",
                 employeeId, cardInfoPayload.getCardId(), cardInfoPayload.getCardType(), cardInfoPayload.getTechnology());

        // 2. Find the partner session
        Session partnerSession = nfcSessionManager.getPartnerSession(session);
        if (partnerSession == null || !partnerSession.isOpen()) {
            log.warn("Cannot forward card info. Partner session not found or closed for employeeId: {}", employeeId);
            sendMessage(session, MessageTypeEnum.ERROR, new ErrorPayload("PARTNER_UNAVAILABLE", "伙伴设备已离线，无法同步卡片信息"));
            return;
        }

        // 3. Forward the card info to partner
        sendMessage(partnerSession, MessageTypeEnum.CARD_INFO, cardInfoPayload);
        log.info("Forwarded card info to partner. employeeId: {}, cardId: {}", employeeId, cardInfoPayload.getCardId());

        // 4. Send confirmation back to sender
        sendMessage(session, MessageTypeEnum.STATUS_UPDATE, new StatusUpdatePayload("CARD_INFO_SENT", "卡片信息已发送给伙伴设备"));
    }

    @OnClose
    public void onClose(Session session) {
        Long employeeId = (Long) session.getUserProperties().get("employeeId");
        if (employeeId == null) {
            log.info("WebSocket onClose: No employeeId found for session={}", session.getId());
            return;
        }
        log.info("WebSocket onClose: employeeId={}, session={}", employeeId, session.getId());

        // Retrieve partner and transaction info BEFORE fully removing the session
        Session partnerSession = nfcSessionManager.getPartnerSession(session);
        Long transactionId = nfcSessionManager.getTransactionId(employeeId);

        // Notify partner if they are still connected
        if (partnerSession != null && partnerSession.isOpen()) {
            log.info("Notifying partner about disconnection. employeeId={}, partnerSession={}", employeeId, partnerSession.getId());
            sendMessage(partnerSession, MessageTypeEnum.STATUS_UPDATE, new StatusUpdatePayload("PARTNER_DISCONNECTED", "对方设备已断开连接"));
            // We don't close the partner session here, let its own onClose handle the cleanup.
        }
        
        // Clean up the session from the manager
        nfcSessionManager.removeSession(session);

        // Attempt to mark the transaction as interrupted if it's still running
        if (transactionId != null) {
            transactionsService.updateTransactionAsFinished(transactionId, "INTERRUPTED", "Connection closed unexpectedly");
        }
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("WebSocket error for session: " + session.getId(), throwable);
        // Consider closing the session on error
        closeSession(session, "Internal server error");
    }

    /**
     * Sends a message to the specified session.
     *
     * @param session the WebSocket session
     * @param type    the message type
     * @param payload the message payload
     */
    private void sendMessage(Session session, MessageTypeEnum type, Object payload) {
        WebSocketMessage<Object> message = new WebSocketMessage<>();
        message.setType(type);
        message.setPayload(payload);

        try {
            // Use Jackson ObjectMapper directly to avoid potential classpath issues with SmartJsonUtil
            ObjectMapper objectMapper = new ObjectMapper();
            String msgStr = objectMapper.writeValueAsString(message);

            log.debug("WebSocket sendMessage: employeeId={}, session={}, msg={}",
                    session.getUserProperties().get("employeeId"), session.getId(), msgStr);
            session.getBasicRemote().sendText(msgStr);
        } catch (Exception e) {
            log.error("WebSocket sendMessage error: " + e.getMessage(), e);
        }
    }

    /**
     * Closes the WebSocket session.
     *
     * @param session the session to close
     * @param reason  the reason for closing
     */
    private void closeSession(Session session, String reason) {
        try {
            log.info("Closing WebSocket session: {}, reason: {}", session.getId(), reason);
            session.close();
        } catch (IOException e) {
            log.error("WebSocket closeSession error: " + e.getMessage(), e);
        }
    }
} 