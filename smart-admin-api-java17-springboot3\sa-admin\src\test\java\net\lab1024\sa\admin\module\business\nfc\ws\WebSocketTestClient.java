package net.lab1024.sa.admin.module.business.nfc.ws;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.websocket.ClientEndpoint;
import jakarta.websocket.ContainerProvider;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import java.net.URI;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

/**
 * 一个可重用的 WebSocket 测试客户端, 用于集成测试.
 * <p>
 * 它封装了连接、发送和接收消息的底层细节, 并提供了同步的、可断言的方法.
 * 每个实例代表一个独立的客户端连接.
 * </p>
 * <AUTHOR>
 */
@ClientEndpoint
public class WebSocketTestClient {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final BlockingQueue<JsonNode> receivedMessages = new LinkedBlockingQueue<>();
    private Session session;

    /**
     * 连接到 WebSocket 服务器.
     * @param uri 服务器端点 URI.
     * @throws Exception 如果连接失败.
     */
    public void connect(String uri) throws Exception {
        ContainerProvider.getWebSocketContainer().connectToServer(this, new URI(uri));
    }

    /**
     * 等待直到连接成功建立.
     * @param timeoutSeconds 超时秒数.
     * @throws InterruptedException 如果线程被中断.
     */
    public void awaitConnect(int timeoutSeconds) throws InterruptedException {
        long end = System.currentTimeMillis() + timeoutSeconds * 1000L;
        while (session == null || !session.isOpen()) {
            if (System.currentTimeMillis() > end) {
                throw new IllegalStateException("客户端连接超时");
            }
            Thread.sleep(100);
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        System.out.println("测试客户端: WebSocket 会话已打开: " + session.getId());
        this.session = session;
    }

    @OnMessage
    public void onMessage(String message) throws Exception {
        System.out.println("测试客户端: 收到消息: " + message);
        receivedMessages.add(objectMapper.readTree(message));
    }

    /**
     * 发送一个 JSON 格式的消息.
     * @param jsonMessage JSON 字符串消息.
     * @throws Exception 如果发送失败.
     */
    public void sendMessage(String jsonMessage) throws Exception {
        if (session != null && session.isOpen()) {
            session.getBasicRemote().sendText(jsonMessage);
            System.out.println("测试客户端: 发送消息: " + jsonMessage);
        } else {
            throw new IllegalStateException("会话未连接或已关闭");
        }
    }

    /**
     * 等待并返回下一个收到的消息.
     * @param timeoutSeconds 超时秒数.
     * @return 收到的消息的 JsonNode.
     * @throws InterruptedException 如果在等待时被中断.
     */
    public JsonNode awaitNextMessage(int timeoutSeconds) throws InterruptedException {
        JsonNode message = receivedMessages.poll(timeoutSeconds, TimeUnit.SECONDS);
        if (message == null) {
            throw new IllegalStateException("在 " + timeoutSeconds + " 秒内未收到任何消息.");
        }
        return message;
    }

    /**
     * 等待一个满足特定条件的消息.
     * 会消耗并忽略队列中不满足条件的消息.
     *
     * @param condition Predicate 条件.
     * @param timeoutSeconds 总超时时间.
     * @return 满足条件的 JsonNode 消息.
     * @throws InterruptedException 如果线程被中断.
     */
    public JsonNode awaitMessageWithCondition(Predicate<JsonNode> condition, int timeoutSeconds) throws InterruptedException {
        long deadline = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(timeoutSeconds);
        while (System.currentTimeMillis() < deadline) {
            long remaining = deadline - System.currentTimeMillis();
            if (remaining <= 0) break;

            JsonNode message = receivedMessages.poll(remaining, TimeUnit.MILLISECONDS);
            if (message != null) {
                if (condition.test(message)) {
                    return message;
                }
            }
        }
        throw new IllegalStateException("在 " + timeoutSeconds + " 秒内未收到满足条件的消息");
    }


    /**
     * 关闭 WebSocket 连接.
     * @throws Exception 如果关闭失败.
     */
    public void close() throws Exception {
        if (session != null && session.isOpen()) {
            session.close();
            System.out.println("测试客户端: WebSocket 会话已关闭.");
        }
    }

    /**
     * 检查客户端是否已连接.
     * @return 如果已连接则为 true.
     */
    public boolean isConnected() {
        return session != null && session.isOpen();
    }
}