[2025-06-29 22:41:35,048][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:41:35,115][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 41032 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:41:35,128][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "@profiles.active@" 
[2025-06-29 22:41:36,407][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:41:36,409][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:41:36,436][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:41:37,991][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:41:40,391][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:41:40,560][INFO ][main][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:41:40,565][INFO ][main][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-29 22:46:23,370][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:46:23,447][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 41440 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:46:23,460][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "@profiles.active@" 
[2025-06-29 22:46:24,680][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:46:24,680][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:46:24,705][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:46:26,206][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:46:28,548][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:46:28,732][INFO ][main][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:46:28,738][INFO ][main][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-29 22:48:25,908][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:48:25,953][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 41076 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:48:25,963][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "@profiles.active@" 
[2025-06-29 22:48:27,161][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:48:27,163][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:48:27,190][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:48:28,697][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:48:31,143][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:48:31,280][INFO ][main][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:48:31,293][INFO ][main][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
