package net.lab1024.sa.admin.module.business.nfc.transactions.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.nfc.transactions.dao.TransactionsDao;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.form.TransactionsQueryForm;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.entity.TransactionsEntity;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.vo.TransactionsVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * NFC中继交易记录表 Service
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:41:36
 * @Copyright xueh所有
 */

@Service
public class TransactionsService {

    @Resource
    private TransactionsDao transactionsDao;

    /**
     * 分页查询
     */
    public PageResult<TransactionsVO> queryPage(TransactionsQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<TransactionsVO> list = transactionsDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * Creates a new transaction record when a WebSocket pair is formed.
     *
     * @param userId The ID of the user initiating the transaction.
     * @return The newly created TransactionsEntity.
     */
    @Transactional
    public TransactionsEntity createTransaction(Long userId) {
        TransactionsEntity transaction = new TransactionsEntity();
        transaction.setTransactionUid(UUID.randomUUID().toString());
        transaction.setUserId(userId);
        transaction.setStatus("RELAYING"); // Initial status
        transaction.setStartTime(LocalDateTime.now());
        transaction.setCreatedAt(LocalDateTime.now());
        transaction.setUpdatedAt(LocalDateTime.now());

        transactionsDao.insert(transaction);
        return transaction;
    }

    /**
     * Updates a transaction record when the session ends.
     *
     * @param transactionId The ID of the transaction to update.
     * @param status        The final status (e.g., "SUCCESS", "FAILED", "CANCELLED").
     * @param failureReason The reason for failure, if any.
     */
    @Transactional
    public void updateTransactionAsFinished(Long transactionId, String status, String failureReason) {
        // To prevent a race condition where a late onClose event overwrites a final status,
        // we first check the current status of the transaction.
        TransactionsEntity currentTransaction = transactionsDao.selectById(transactionId);
        if (currentTransaction == null) {
            return; // Or log an error, transaction not found
        }

        // Only update if the transaction is still in a "RELAYING" state,
        // or if the new status is not "INTERRUPTED". This allows final statuses to be set,
        // but prevents "INTERRUPTED" from overwriting a "SUCCESS" or "FAILED" status.
        if (!"RELAYING".equals(currentTransaction.getStatus()) && "INTERRUPTED".equals(status)) {
            return;
        }

        TransactionsEntity transactionToUpdate = new TransactionsEntity();
        transactionToUpdate.setId(transactionId);
        transactionToUpdate.setStatus(status);
        transactionToUpdate.setFailureReason(failureReason);
        transactionToUpdate.setEndTime(LocalDateTime.now());
        transactionToUpdate.setUpdatedAt(LocalDateTime.now());

        transactionsDao.updateById(transactionToUpdate);
    }
}
