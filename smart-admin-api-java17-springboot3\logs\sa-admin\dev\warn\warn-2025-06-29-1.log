[2025-06-29 07:15:33,042][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:33,047][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:33,052][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:33,056][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:33,058][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:33,061][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:33,488][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:33,982][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 07:15:34,387][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,392][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,403][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,409][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,417][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,425][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,430][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,433][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,437][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,444][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,446][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:34,450][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 07:15:39,470][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 07:15:39,697][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://**************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-29 08:47:58,291][WARN ][SpringApplicationShutdownHook][o.s.s.c.ThreadPoolTaskScheduler:366] Timed out while waiting for executor to terminate 
[2025-06-29 08:48:03,323][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:03,329][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:03,333][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:03,338][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:03,340][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:03,342][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:03,814][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,354][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 08:48:04,662][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,666][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,674][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,678][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,684][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,691][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,696][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,699][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,703][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,710][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,712][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:04,716][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 08:48:11,998][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 08:48:12,202][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://**************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-29 17:06:27,051][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:27,056][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:27,060][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:27,065][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:27,067][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:27,069][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:27,526][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,008][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 17:06:28,326][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,329][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,338][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,342][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,348][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,354][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,360][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,362][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,366][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,374][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,376][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:28,380][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 17:06:32,918][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 17:06:33,048][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://**************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
[2025-06-29 21:17:20,266][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:20,273][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:20,276][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:20,281][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:20,283][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:20,285][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:20,764][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,290][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 21:17:21,508][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,512][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,520][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,524][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,529][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,537][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,542][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,545][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,548][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy131] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,556][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,557][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy132] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:21,561][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 21:17:26,365][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 21:17:26,511][WARN ][main][n.l.s.b.l.WebServerListener:69] 
-------------【sa-admin】 服务已成功启动 （sa-admin started successfully）-------------
	当前启动环境:	dev , 开发环境
	返回码初始化:	完成15个返回码初始化
	服务本机地址:	http://localhost:1024/
	服务外网地址:	http://**************:1024/
	Swagger地址:	http://localhost:1024/swagger-ui/index.html
	knife4j地址:	http://localhost:1024/doc.html
-------------------------------------------------------------------------------------
 
