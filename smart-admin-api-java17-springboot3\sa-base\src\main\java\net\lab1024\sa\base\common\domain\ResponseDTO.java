package net.lab1024.sa.base.common.domain;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.code.ErrorCode;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.enumeration.DataTypeEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 请求返回对象
 *
 * <AUTHOR> 卓大
 * @Date 2021-10-31 21:06:11
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@Schema
public class ResponseDTO<T> {

    public static final int OK_CODE = 0;

    public static final String OK_MSG = "操作成功";

    @Schema(description = """
           响应状态码：
           • 0 - 操作成功
           • 10001 - 系统错误
           • 20001 - 业务繁忙，请稍后重试  
           • 20002 - 付款单id发生了异常，请联系技术人员排查
           • 30001 - 参数错误
           • 30002 - 数据不存在
           • 30003 - 数据已存在
           • 30004 - 操作太快，请稍等
           • 30005 - 没有权限
           • 30006 - 系统正在开发中
           • 30007 - 未登录或登录失效
           • 30008 - 用户状态异常
           • 30009 - 请勿重复提交
           • 30010 - 登录连续失败已被锁定
           • 30011 - 登录连续失败将会锁定提醒
           • 30012 - 长时间未操作系统，需要重新登录
           """, 
           example = "0",
           allowableValues = {"0", "10001", "20001", "20002", "30001", "30002", "30003", "30004", "30005", "30006", "30007", "30008", "30009", "30010", "30011", "30012"})
    private Integer code;

    @Schema(description = """
           错误级别：
           • null - 操作成功时为空
           • system - 系统级别错误（代码bug，需要程序员修复）
           • unexpected - 意外错误级别（不可预期的异常，需要技术人员排查）  
           • user - 用户级别错误（用户操作不当引起，用户可自行解决）
           """, 
           example = "user",
           allowableValues = {"null", "system", "unexpected", "user"})
    private String level;

    @Schema(description = "响应消息", 
           example = "操作成功")
    private String msg;

    @Schema(description = "操作是否成功", 
           example = "true")
    private Boolean ok;

    @Schema(description = "返回数据")
    private T data;

    @SchemaEnum(value = DataTypeEnum.class,desc = "数据类型：1-普通数据，10-加密数据")
    private Integer dataType;

    public ResponseDTO() {
    }

    public ResponseDTO(Integer code, String level, boolean ok, String msg, T data) {
        this.code = code;
        this.level = level;
        this.ok = ok;
        this.msg = msg;
        this.data = data;
        this.dataType = DataTypeEnum.NORMAL.getValue();
    }

    public ResponseDTO(Integer code, String level, boolean ok, String msg) {
        this.code = code;
        this.level = level;
        this.ok = ok;
        this.msg = msg;
        this.dataType = DataTypeEnum.NORMAL.getValue();
    }

    public ResponseDTO(ErrorCode errorCode, boolean ok, String msg, T data) {
        this.code = errorCode.getCode();
        this.level = errorCode.getLevel();
        this.ok = ok;
        if (StringUtils.isNotBlank(msg)) {
            this.msg = msg;
        } else {
            this.msg = errorCode.getMsg();
        }
        this.data = data;
        this.dataType = DataTypeEnum.NORMAL.getValue();
    }

    public static <T> ResponseDTO<T> ok() {
        return new ResponseDTO<>(OK_CODE, null, true, OK_MSG, null);
    }

    public static <T> ResponseDTO<T> ok(T data) {
        return new ResponseDTO<>(OK_CODE, null, true, OK_MSG, data);
    }

    public static <T> ResponseDTO<T> okMsg(String msg) {
        return new ResponseDTO<>(OK_CODE, null, true, msg, null);
    }

    // -------------------------------------------- 最常用的 用户参数 错误码 --------------------------------------------

    public static <T> ResponseDTO<T> userErrorParam() {
        return new ResponseDTO<>(UserErrorCode.PARAM_ERROR, false, null, null);
    }


    public static <T> ResponseDTO<T> userErrorParam(String msg) {
        return new ResponseDTO<>(UserErrorCode.PARAM_ERROR, false, msg, null);
    }

    // -------------------------------------------- 错误码 --------------------------------------------

    public static <T> ResponseDTO<T> error(ErrorCode errorCode) {
        return new ResponseDTO<>(errorCode, false, null, null);
    }

    public static <T> ResponseDTO<T> error(ErrorCode errorCode, boolean ok) {
        return new ResponseDTO<>(errorCode, ok, null, null);
    }

    public static <T>  ResponseDTO<T> error(ResponseDTO<?> responseDTO) {
        return new ResponseDTO<>(responseDTO.getCode(), responseDTO.getLevel(), responseDTO.getOk(), responseDTO.getMsg(), null);
    }

    public static <T> ResponseDTO<T> error(ErrorCode errorCode, String msg) {
        return new ResponseDTO<>(errorCode, false, msg, null);
    }

    public static <T> ResponseDTO<T> errorData(ErrorCode errorCode, T data) {
        return new ResponseDTO<>(errorCode, false, null, data);
    }


}
