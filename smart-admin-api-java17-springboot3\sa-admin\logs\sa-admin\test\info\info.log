[2025-06-29 22:53:02,340][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:53:02,402][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 12672 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:53:02,409][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "test" 
[2025-06-29 22:53:03,660][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:53:03,662][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:53:03,691][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:53:05,149][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:53:07,736][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:53:08,331][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 22:53:08,672][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for ************/************:6379 
[2025-06-29 22:53:09,329][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for ************/************:6379 
[2025-06-29 22:53:10,869][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 22:53:11,599][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 22:53:13,019][INFO ][main][o.s.b.t.m.w.SpringBootMockServletContext:437] Initializing Spring TestDispatcherServlet '' 
[2025-06-29 22:53:13,019][INFO ][main][o.s.t.w.s.TestDispatcherServlet:532] Initializing Servlet '' 
[2025-06-29 22:53:13,025][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 22:53:13,037][INFO ][main][o.s.t.w.s.TestDispatcherServlet:554] Completed initialization in 17 ms 
[2025-06-29 22:53:13,110][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:56] Started UserRegisterControllerTests in 11.214 seconds (process running for 12.138) 
[2025-06-29 22:53:14,363][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-29 22:53:14,395][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:53:14,400][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-29 22:54:31,747][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:54:31,813][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 5728 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:54:31,821][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "test" 
[2025-06-29 22:54:33,074][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:54:33,077][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:54:33,101][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:54:34,562][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:54:36,866][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:54:37,269][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 22:54:37,539][INFO ][redisson-netty-2-8][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for ************/************:6379 
[2025-06-29 22:54:38,279][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for ************/************:6379 
[2025-06-29 22:54:40,212][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 22:54:40,894][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 22:54:42,697][INFO ][main][o.s.b.t.m.w.SpringBootMockServletContext:437] Initializing Spring TestDispatcherServlet '' 
[2025-06-29 22:54:42,697][INFO ][main][o.s.t.w.s.TestDispatcherServlet:532] Initializing Servlet '' 
[2025-06-29 22:54:42,703][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 22:54:42,712][INFO ][main][o.s.t.w.s.TestDispatcherServlet:554] Completed initialization in 15 ms 
[2025-06-29 22:54:42,789][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:56] Started UserRegisterControllerTests in 11.51 seconds (process running for 12.47) 
[2025-06-29 22:54:43,883][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-29 22:54:43,913][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:54:43,917][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-29 22:59:24,096][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 22:59:24,154][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 41580 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 22:59:24,161][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "test" 
[2025-06-29 22:59:25,488][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 22:59:25,491][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 22:59:25,520][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces. 
[2025-06-29 22:59:26,986][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 22:59:29,390][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 22:59:30,048][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 22:59:30,320][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for ************/************:6379 
[2025-06-29 22:59:30,978][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for ************/************:6379 
[2025-06-29 22:59:32,953][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 22:59:33,689][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 22:59:35,326][INFO ][main][o.s.b.t.m.w.SpringBootMockServletContext:437] Initializing Spring TestDispatcherServlet '' 
[2025-06-29 22:59:35,326][INFO ][main][o.s.t.w.s.TestDispatcherServlet:532] Initializing Servlet '' 
[2025-06-29 22:59:35,333][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 22:59:35,344][INFO ][main][o.s.t.w.s.TestDispatcherServlet:554] Completed initialization in 16 ms 
[2025-06-29 22:59:35,441][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:56] Started UserRegisterControllerTests in 11.796 seconds (process running for 12.778) 
[2025-06-29 22:59:36,653][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-29 22:59:36,681][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 22:59:36,686][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-29 23:01:57,715][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-29 23:01:57,796][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:50] Starting UserRegisterControllerTests using Java 17.0.13 with PID 43160 (started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin) 
[2025-06-29 23:01:57,803][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:660] The following 1 profile is active: "test" 
[2025-06-29 23:01:59,049][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-29 23:01:59,049][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-29 23:01:59,076][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces. 
[2025-06-29 23:02:02,034][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-29 23:02:04,896][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-29 23:02:05,668][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-29 23:02:06,066][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for ************/************:6379 
[2025-06-29 23:02:06,765][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for ************/************:6379 
[2025-06-29 23:02:08,722][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-29 23:02:09,470][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-29 23:02:10,867][INFO ][main][o.s.b.t.m.w.SpringBootMockServletContext:437] Initializing Spring TestDispatcherServlet '' 
[2025-06-29 23:02:10,867][INFO ][main][o.s.t.w.s.TestDispatcherServlet:532] Initializing Servlet '' 
[2025-06-29 23:02:10,874][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-29 23:02:10,887][INFO ][main][o.s.t.w.s.TestDispatcherServlet:554] Completed initialization in 20 ms 
[2025-06-29 23:02:10,945][INFO ][main][n.l.s.a.m.s.e.c.UserRegisterControllerTests:56] Started UserRegisterControllerTests in 13.705 seconds (process running for 14.64) 
[2025-06-29 23:02:12,545][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-29 23:02:12,574][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-29 23:02:12,577][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
