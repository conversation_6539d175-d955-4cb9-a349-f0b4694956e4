package net.lab1024.sa.admin.module.business.nfc.userdevices.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户登录设备表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:47:54
 * @Copyright xueh所有
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class UserDevicesQueryForm extends PageParam {

    @Schema(description = "关联的用户ID")
    private Long userId;

    @Schema(description = "设备型号 (如: 'Google Pixel 8')")
    private String deviceModel;

}
