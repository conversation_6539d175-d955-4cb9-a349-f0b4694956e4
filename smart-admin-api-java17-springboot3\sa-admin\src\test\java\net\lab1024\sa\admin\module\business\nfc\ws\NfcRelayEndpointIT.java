package net.lab1024.sa.admin.module.business.nfc.ws;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.lab1024.sa.admin.module.business.nfc.transactions.dao.TransactionsDao;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.entity.TransactionsEntity;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.MessageTypeEnum;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.RoleRegisterPayload;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.SessionEndPayload;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.WebSocketMessage;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.ApduPayload;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.ClientStateUpdatePayload;
import net.lab1024.sa.admin.module.system.login.domain.LoginForm;
import net.lab1024.sa.admin.module.system.login.domain.LoginResultVO;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.employee.domain.form.EmployeeAddForm;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.admin.module.system.role.service.RoleEmployeeService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.constant.LoginDeviceEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.net.URI;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link NfcRelayEndpoint} 的端到端集成测试.
 * <p>
 * 这个测试类模拟了真实的WebSocket客户端行为,
 * 在一个真实的Spring Boot测试环境中验证完整的业务流程,
 * 并直接断言数据库的状态, 确保数据一致性.
 * </p>
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@DisplayName("NFC 中继端点 (WebSocket) 集成测试")
class NfcRelayEndpointIT {

    @LocalServerPort
    private Integer port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TransactionsDao transactionsDao;

    private String token;
    private Long employeeId;
    private WebSocketClientEndpoint client;
    private WebSocketClientEndpoint client2;


    @BeforeEach
    void setUp() throws Exception {
        // 1. 在数据库中创建一个临时的测试用户
        EmployeeAddForm addForm = new EmployeeAddForm();
        String randomSuffix = UUID.randomUUID().toString().substring(0, 8);
        String loginName = "testuser_" + randomSuffix;
        addForm.setLoginName(loginName);
        addForm.setActualName("测试用户" + randomSuffix);
        long randomPhoneSuffix = ThreadLocalRandom.current().nextLong(10000000L, 100000000L);
        addForm.setPhone("199" + randomPhoneSuffix);
        addForm.setDepartmentId(1L); // 假设默认部门ID为1
        addForm.setDisabledFlag(false);
        addForm.setEmail(randomSuffix + "@test.com");

        ResponseDTO<String> response = employeeService.addEmployee(addForm);
        assertTrue(response.getOk(), "创建测试用户失败: " + response.getMsg());

        // 通过loginName获取刚创建的用户以得到ID
        EmployeeEntity newEmployee = employeeService.getByLoginName(loginName);
        assertNotNull(newEmployee, "无法通过loginName找到刚创建的测试用户");
        this.employeeId = newEmployee.getEmployeeId();

        // 2. 使用 Sa-Token 直接为该用户登录，绕过UI和密码验证
        // 在测试环境中，手动模拟一个Web上下文，以允许Sa-Token正常工作
        MockHttpServletRequest request = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
        try {
            StpUtil.login(this.employeeId);
            // 为测试会话手动注入真实的业务场景中会存在的 'loginDevice' 信息，以修复 StpUtil.getExtra() 异常
            StpUtil.getSession().set("loginDevice", LoginDeviceEnum.PC.getValue());
            this.token = StpUtil.getTokenValue();
        } finally {
            // 清理模拟的Web上下文，避免影响其他测试
            RequestContextHolder.resetRequestAttributes();
        }
        assertNotNull(token, "通过StpUtil登录后应获取到token");
    }

    @AfterEach
    void tearDown() throws Exception {
        // 清理测试客户端
        if (client != null && client.isOpen()) {
            client.closeBlocking();
        }
        if (client2 != null && client2.isOpen()) {
            client2.closeBlocking();
        }

        // 清理数据库中的测试用户（软删除）
        if (this.employeeId != null) {
            employeeService.batchUpdateDeleteFlag(Collections.singletonList(this.employeeId));
        }
    }

    @Test
    @DisplayName("客户端注册角色后应收到等待伙伴的状态")
    void testClientReceivesWaitingStateAfterRegisteringRole() throws Exception {
        // 1. 客户端连接
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client.connectBlocking();

        // 2. 验证并消费掉第一条"连接成功"的消息
        JsonNode connectionMessage = client.awaitNextMessage(3);
        assertNotNull(connectionMessage, "客户端连接后应在3秒内收到认证成功消息");
        assertEquals("STATUS_UPDATE", connectionMessage.get("type").asText());
        assertEquals("CONNECTED", connectionMessage.get("payload").get("status").asText(), "连接后的第一条消息应该是认证成功");

        // 3. 客户端发送"注册角色"的消息
        // 使用 ObjectMapper 来序列化对象，以确保格式（包括@class类型信息）正确
        RoleRegisterPayload rolePayload = new RoleRegisterPayload();
        rolePayload.setRole("TRANSMITTER");
        WebSocketMessage<RoleRegisterPayload> message = new WebSocketMessage<>(MessageTypeEnum.REGISTER_ROLE, rolePayload);
        
        String registerMessage = objectMapper.writeValueAsString(message);
        client.sendMessage(registerMessage);

        // 4. 断言收到了"等待伙伴"的状态更新
        JsonNode waitingMessage = client.awaitNextMessage(3);
        assertNotNull(waitingMessage, "注册角色后应在3秒内收到等待伙伴消息");
        assertEquals("STATUS_UPDATE", waitingMessage.get("type").asText());
        assertEquals("WAITING_FOR_PARTNER", waitingMessage.get("payload").get("status").asText(), "注册角色后，正确的状态应该是等待伙伴");
    }

    @Test
    @DisplayName("两个客户端使用同一用户token注册角色后应成功配对")
    void testTwoClientsPairSuccessfully() throws Exception {
        // 1. 启动两个客户端并连接
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client2 = new WebSocketClientEndpoint(uri);

        assertTrue(client.connectBlocking(3, TimeUnit.SECONDS), "客户端1应在3秒内连接成功");
        assertTrue(client2.connectBlocking(3, TimeUnit.SECONDS), "客户端2应在3秒内连接成功");

        // 消费掉两个客户端的 "CONNECTED" 消息
        assertNotNull(client.awaitNextMessage(3), "客户端1应收到连接成功消息");
        assertNotNull(client2.awaitNextMessage(3), "客户端2应收到连接成功消息");

        // 2. 客户端1 (读卡端) 注册角色
        WebSocketMessage<RoleRegisterPayload> registerMsg1 = new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE,
                new RoleRegisterPayload("TRANSMITTER")
        );
        client.sendMessage(objectMapper.writeValueAsString(registerMsg1));
        // 消费掉客户端1的 "WAITING_FOR_PARTNER" 消息
        assertNotNull(client.awaitNextMessage(3), "客户端1注册后应收到等待消息");


        // 3. 客户端2 (模拟端) 注册角色
        WebSocketMessage<RoleRegisterPayload> registerMsg2 = new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE,
                new RoleRegisterPayload("RECEIVER")
        );
        client2.sendMessage(objectMapper.writeValueAsString(registerMsg2));

        // 4. 断言两个客户端都收到了配对成功的消息
        JsonNode pairedMsg1 = client.awaitNextMessage(5);
        JsonNode pairedMsg2 = client2.awaitNextMessage(5);

        assertNotNull(pairedMsg1, "客户端1应收到配对消息");
        assertEquals("PAIRED", pairedMsg1.get("type").asText());

        assertNotNull(pairedMsg2, "客户端2应收到配对消息");
        assertEquals("PAIRED", pairedMsg2.get("type").asText());

        // 5. 验证数据库中创建了交易记录
        List<TransactionsEntity> transactions = transactionsDao.selectList(
                new LambdaQueryWrapper<TransactionsEntity>().eq(TransactionsEntity::getUserId, this.employeeId)
        );
        assertEquals(1, transactions.size(), "配对成功后，数据库中应创建一条交易记录");
        assertEquals("RELAYING", transactions.get(0).getStatus(), "新创建的交易状态应为 RELAYING");
    }

    @Test
    @DisplayName("APDU指令应在配对的客户端之间正确中继")
    void testApduCommandsAreRelayedCorrectly() throws Exception {
        // 1. 建立并完成配对 (与上一个测试类似)
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client2 = new WebSocketClientEndpoint(uri);
        client.connectBlocking(3, TimeUnit.SECONDS);
        client2.connectBlocking(3, TimeUnit.SECONDS);
        client.awaitNextMessage(3); // Consume CONNECTED
        client2.awaitNextMessage(3); // Consume CONNECTED

        // Client 1 (Transmitter) registers
        client.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("TRANSMITTER")
        )));
        client.awaitNextMessage(3); // Consume WAITING_FOR_PARTNER

        // Client 2 (Receiver) registers to complete pairing
        client2.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("RECEIVER")
        )));
        client.awaitNextMessage(3); // Consume PAIRED
        client2.awaitNextMessage(3); // Consume PAIRED

        // 2. 模拟 Receiver (client2) 发送APDU指令
        String apduCommand = "00A4040007A0000002471001";
        ApduPayload commandPayload = new ApduPayload(apduCommand);
        WebSocketMessage<ApduPayload> commandMessage = new WebSocketMessage<>(MessageTypeEnum.APDU_COMMAND, commandPayload);
        client2.sendMessage(objectMapper.writeValueAsString(commandMessage));

        // 3. 断言 Transmitter (client) 收到了正确的APDU指令
        JsonNode receivedCommand = client.awaitNextMessage(5);
        assertNotNull(receivedCommand, "读卡端应在5秒内收到转发的APDU指令");
        assertEquals("APDU_COMMAND", receivedCommand.get("type").asText());
        assertEquals(apduCommand, receivedCommand.get("payload").get("apdu").asText(), "读卡端收到的APDU指令内容不匹配");

        // 4. 模拟 Transmitter (client) 发送APDU响应
        String apduResponse = "9000";
        ApduPayload responsePayload = new ApduPayload(apduResponse);
        WebSocketMessage<ApduPayload> responseMessage = new WebSocketMessage<>(MessageTypeEnum.APDU_RESPONSE, responsePayload);
        client.sendMessage(objectMapper.writeValueAsString(responseMessage));

        // 5. 断言 Receiver (client2) 收到了正确的APDU响应
        JsonNode receivedResponse = client2.awaitNextMessage(5);
        assertNotNull(receivedResponse, "模拟端应在5秒内收到转发的APDU响应");
        assertEquals("APDU_RESPONSE", receivedResponse.get("type").asText());
        assertEquals(apduResponse, receivedResponse.get("payload").get("apdu").asText(), "模拟端收到的APDU响应内容不匹配");
    }

    @Test
    @DisplayName("当一个客户端断开连接时，其伙伴应收到通知")
    void testPartnerIsNotifiedOnClientDisconnection() throws Exception {
        // 1. 建立并完成配对
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client2 = new WebSocketClientEndpoint(uri);
        client.connectBlocking(3, TimeUnit.SECONDS);
        client2.connectBlocking(3, TimeUnit.SECONDS);
        client.awaitNextMessage(3); // Consume CONNECTED
        client2.awaitNextMessage(3); // Consume CONNECTED
        client.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("TRANSMITTER")
        )));
        client.awaitNextMessage(3); // Consume WAITING_FOR_PARTNER
        client2.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("RECEIVER")
        )));
        client.awaitNextMessage(3); // Consume PAIRED
        client2.awaitNextMessage(3); // Consume PAIRED

        // 2. 突然关闭一个客户端的连接
        client2.closeBlocking();

        // 3. 断言另一个客户端收到了伙伴断开连接的通知
        JsonNode disconnectMsg = client.awaitNextMessage(5);
        assertNotNull(disconnectMsg, "伙伴客户端应在5秒内收到断连通知");
        assertEquals("STATUS_UPDATE", disconnectMsg.get("type").asText());
        assertEquals("PARTNER_DISCONNECTED", disconnectMsg.get("payload").get("status").asText(), "通知的状态应为伙伴已断开");

        // 4. 使用轮询和超时来验证数据库状态，以处理异步更新
        long startTime = System.currentTimeMillis();
        List<TransactionsEntity> transactions;
        while (true) {
            transactions = transactionsDao.selectList(
                    new LambdaQueryWrapper<TransactionsEntity>().eq(TransactionsEntity::getUserId, this.employeeId)
            );
            // 检查是否查询到了记录并且状态是否正确
            if (!transactions.isEmpty() && "INTERRUPTED".equals(transactions.get(0).getStatus())) {
                break; // 状态正确，跳出循环
            }
            // 检查是否超时
            if (System.currentTimeMillis() - startTime > 5000) {
                fail("交易状态未在5秒内更新为 INTERRUPTED. 当前状态: " + (transactions.isEmpty() ? "未找到记录" : transactions.get(0).getStatus()));
            }
            Thread.sleep(200); // 轮询间隔
        }

        // 如果循环正常结束，说明状态已正确，可以进行最终确认性断言
        assertEquals(1, transactions.size());
        assertEquals("INTERRUPTED", transactions.get(0).getStatus());
    }

    @Test
    @DisplayName("当已有配对存在时，第三个客户端注册角色应被拒绝")
    void testThirdClientIsRejectedWhenPairExists() throws Exception {
        // 1. 建立并完成配对
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client2 = new WebSocketClientEndpoint(uri);
        assertTrue(client.connectBlocking(3, TimeUnit.SECONDS), "客户端1应成功连接");
        assertTrue(client2.connectBlocking(3, TimeUnit.SECONDS), "客户端2应成功连接");
        client.awaitNextMessage(3); // Consume CONNECTED
        client2.awaitNextMessage(3); // Consume CONNECTED
        client.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("TRANSMITTER")
        )));
        client.awaitNextMessage(3); // Consume WAITING_FOR_PARTNER
        client2.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("RECEIVER")
        )));
        client.awaitNextMessage(3); // Consume PAIRED
        client2.awaitNextMessage(3); // Consume PAIRED

        // 2. 启动第三个客户端并尝试注册角色
        WebSocketClientEndpoint client3 = new WebSocketClientEndpoint(uri);
        try {
            assertTrue(client3.connectBlocking(3, TimeUnit.SECONDS), "客户端3应成功连接");
            client3.awaitNextMessage(3); // Consume 客户端3自己的 CONNECTED 消息

            client3.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                    MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("TRANSMITTER")
            )));

            // 3. 断言第三个客户端收到了错误消息
            JsonNode errorMsg = client3.awaitNextMessage(5);
            assertNotNull(errorMsg, "第三个客户端应收到错误消息");
            assertEquals("ERROR", errorMsg.get("type").asText());
            assertEquals("SESSION_ALREADY_ACTIVE", errorMsg.get("payload").get("code").asText(), "错误码应为会话已存在");

            // 4. 断言原始配对的客户端未受影响 (可选但推荐)
            String apduCommand = "00A4040000"; // A simple PING command
            client2.sendMessage(objectMapper.writeValueAsString(
                    new WebSocketMessage<>(MessageTypeEnum.APDU_COMMAND, new ApduPayload(apduCommand))
            ));
            JsonNode relayedCommand = client.awaitNextMessage(5);
            assertNotNull(relayedCommand, "原始配对的会话应仍然活跃");
            assertEquals("APDU_COMMAND", relayedCommand.get("type").asText());
        } finally {
            if (client3.isOpen()) {
                client3.closeBlocking();
            }
        }
    }

    @Test
    @DisplayName("当客户端主动发送结束会话请求时，会话应正确终止")
    void testSessionEndsCorrectlyOnClientRequest() throws Exception {
        // 1. 建立并完成配对
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client2 = new WebSocketClientEndpoint(uri);
        client.connectBlocking(3, TimeUnit.SECONDS);
        client2.connectBlocking(3, TimeUnit.SECONDS);
        client.awaitNextMessage(3); // Consume CONNECTED
        client2.awaitNextMessage(3); // Consume CONNECTED
        client.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("TRANSMITTER")
        )));
        client.awaitNextMessage(3); // Consume WAITING_FOR_PARTNER
        client2.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("RECEIVER")
        )));
        client.awaitNextMessage(3); // Consume PAIRED
        client2.awaitNextMessage(3); // Consume PAIRED

        // 2. 模拟一个客户端 (client1) 发送结束会话的消息
        String finalStatus = "FAILED";
        String failureReason = "NFC_TAG_LOST";
        SessionEndPayload sessionEndPayload = new SessionEndPayload();
        sessionEndPayload.setStatus(finalStatus);
        sessionEndPayload.setReason(failureReason);
        WebSocketMessage<SessionEndPayload> sessionEndMessage = new WebSocketMessage<>(MessageTypeEnum.SESSION_END, sessionEndPayload);
        client.sendMessage(objectMapper.writeValueAsString(sessionEndMessage));

        // 3. 断言另一个客户端 (client2) 收到了会话结束的通知
        JsonNode endMsg = client2.awaitNextMessage(5);
        assertNotNull(endMsg, "伙伴客户端应在5秒内收到会话结束通知");
        assertEquals("SESSION_END", endMsg.get("type").asText());
        assertEquals(finalStatus, endMsg.get("payload").get("status").asText());
        assertEquals(failureReason, endMsg.get("payload").get("reason").asText());

        // 4. 使用轮询验证数据库状态被正确更新
        long startTime = System.currentTimeMillis();
        List<TransactionsEntity> transactions;
        while (true) {
            transactions = transactionsDao.selectList(
                    new LambdaQueryWrapper<TransactionsEntity>().eq(TransactionsEntity::getUserId, this.employeeId)
            );
            if (!transactions.isEmpty() && finalStatus.equals(transactions.get(0).getStatus())) {
                break;
            }
            if (System.currentTimeMillis() - startTime > 5000) {
                fail("交易状态未在5秒内更新为 " + finalStatus);
            }
            Thread.sleep(200);
        }

        // 5. 最终断言数据库记录的完整性
        assertEquals(1, transactions.size());
        assertEquals(finalStatus, transactions.get(0).getStatus(), "交易的最终状态不正确");
        assertEquals(failureReason, transactions.get(0).getFailureReason(), "交易的失败原因不正确");
        assertNotNull(transactions.get(0).getEndTime(), "交易的结束时间应被记录");
    }

    @Test
    @DisplayName("客户端状态更新应正确转发给伙伴设备")
    void testClientStateUpdateIsForwardedToPartner() throws Exception {
        // 1. 建立并完成配对
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client2 = new WebSocketClientEndpoint(uri);
        client.connectBlocking(3, TimeUnit.SECONDS);
        client2.connectBlocking(3, TimeUnit.SECONDS);
        client.awaitNextMessage(3); // Consume CONNECTED
        client2.awaitNextMessage(3); // Consume CONNECTED
        
        // 建立配对: client = Transmitter, client2 = Receiver
        client.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("TRANSMITTER")
        )));
        client.awaitNextMessage(3); // Consume WAITING_FOR_PARTNER
        client2.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("RECEIVER")
        )));
        client.awaitNextMessage(3); // Consume PAIRED
        client2.awaitNextMessage(3); // Consume PAIRED

        // 2. 模拟读卡端 (client) 发送 "卡片已连接" 状态更新
        ClientStateUpdatePayload statePayload = new ClientStateUpdatePayload("CARD_ATTACHED");
        WebSocketMessage<ClientStateUpdatePayload> stateMessage = new WebSocketMessage<>(MessageTypeEnum.CLIENT_STATE_UPDATE, statePayload);
        client.sendMessage(objectMapper.writeValueAsString(stateMessage));

        // 3. 断言模拟端 (client2) 收到了正确的伙伴状态通知
        JsonNode partnerNotification = client2.awaitNextMessage(5);
        assertNotNull(partnerNotification, "模拟端应在5秒内收到伙伴状态通知");
        assertEquals("STATUS_UPDATE", partnerNotification.get("type").asText());
        assertEquals("PARTNER_CARD_ATTACHED", partnerNotification.get("payload").get("status").asText());
        assertEquals("对方设备已准备就绪，请开始操作。", partnerNotification.get("payload").get("message").asText());

        // 4. 模拟读卡端 (client) 发送 "卡片已断开" 状态更新
        ClientStateUpdatePayload detachPayload = new ClientStateUpdatePayload("CARD_DETACHED");
        WebSocketMessage<ClientStateUpdatePayload> detachMessage = new WebSocketMessage<>(MessageTypeEnum.CLIENT_STATE_UPDATE, detachPayload);
        client.sendMessage(objectMapper.writeValueAsString(detachMessage));

        // 5. 断言模拟端 (client2) 收到了正确的断开通知
        JsonNode detachNotification = client2.awaitNextMessage(5);
        assertNotNull(detachNotification, "模拟端应在5秒内收到卡片断开通知");
        assertEquals("STATUS_UPDATE", detachNotification.get("type").asText());
        assertEquals("PARTNER_CARD_DETACHED", detachNotification.get("payload").get("status").asText());
        assertEquals("对方设备卡片已断开。", detachNotification.get("payload").get("message").asText());
    }

    @Test
    @DisplayName("客户端发送无效状态更新应收到错误响应")
    void testInvalidStateUpdateReturnsError() throws Exception {
        // 1. 建立并完成配对
        URI uri = new URI("ws://localhost:" + port + "/ws/nfc/" + this.token);
        client = new WebSocketClientEndpoint(uri);
        client2 = new WebSocketClientEndpoint(uri);
        client.connectBlocking(3, TimeUnit.SECONDS);
        client2.connectBlocking(3, TimeUnit.SECONDS);
        client.awaitNextMessage(3); // Consume CONNECTED
        client2.awaitNextMessage(3); // Consume CONNECTED
        
        client.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("TRANSMITTER")
        )));
        client.awaitNextMessage(3); // Consume WAITING_FOR_PARTNER
        client2.sendMessage(objectMapper.writeValueAsString(new WebSocketMessage<>(
                MessageTypeEnum.REGISTER_ROLE, new RoleRegisterPayload("RECEIVER")
        )));
        client.awaitNextMessage(3); // Consume PAIRED
        client2.awaitNextMessage(3); // Consume PAIRED

        // 2. 发送无效的状态更新
        ClientStateUpdatePayload invalidPayload = new ClientStateUpdatePayload("INVALID_STATE");
        WebSocketMessage<ClientStateUpdatePayload> invalidMessage = new WebSocketMessage<>(MessageTypeEnum.CLIENT_STATE_UPDATE, invalidPayload);
        client.sendMessage(objectMapper.writeValueAsString(invalidMessage));

        // 3. 断言收到错误响应
        JsonNode errorResponse = client.awaitNextMessage(5);
        assertNotNull(errorResponse, "客户端应在5秒内收到错误响应");
        assertEquals("ERROR", errorResponse.get("type").asText());
        assertEquals("INVALID_STATE", errorResponse.get("payload").get("code").asText());
        assertEquals("无效的状态类型", errorResponse.get("payload").get("message").asText());
    }
}
