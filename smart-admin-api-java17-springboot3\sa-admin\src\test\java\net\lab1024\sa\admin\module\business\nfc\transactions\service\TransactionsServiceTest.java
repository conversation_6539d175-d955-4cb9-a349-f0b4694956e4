package net.lab1024.sa.admin.module.business.nfc.transactions.service;

import net.lab1024.sa.admin.module.business.nfc.transactions.dao.TransactionsDao;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.entity.TransactionsEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * {@link TransactionsService} 的单元测试.
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("TransactionsService 单元测试")
class TransactionsServiceTest {

    @Mock
    private TransactionsDao transactionsDao;

    @InjectMocks
    private TransactionsService transactionsService;

    private static final Long TEST_USER_ID = 1L;
    private static final Long TEST_TRANSACTION_ID = 100L;

    @Nested
    @DisplayName("交易创建")
    class CreationTests {

        @Test
        @DisplayName("当调用 createTransaction 时, 应使用正确的初始数据调用 dao.insert")
        void whenCreateTransaction_thenCallsDaoInsertWithCorrectData() {
            // 准备
            System.out.println("--- 测试: createTransaction ---");
            System.out.println("准备: 创建参数捕获器 ArgumentCaptor<TransactionsEntity>");
            ArgumentCaptor<TransactionsEntity> transactionCaptor = ArgumentCaptor.forClass(TransactionsEntity.class);

            // 操作
            System.out.println("操作: 调用 transactionsService.createTransaction()，用户ID: " + TEST_USER_ID);
            transactionsService.createTransaction(TEST_USER_ID);

            // 断言
            verify(transactionsDao).insert(transactionCaptor.capture());
            TransactionsEntity capturedTransaction = transactionCaptor.getValue();
            System.out.println("断言: 验证 transactionsDao.insert() 被调用");
            System.out.println("断言: 捕获到的交易实体为 -> " + capturedTransaction);


            assertEquals(TEST_USER_ID, capturedTransaction.getUserId());
            assertEquals("RELAYING", capturedTransaction.getStatus());
            assertNotNull(capturedTransaction.getTransactionUid());
            assertNotNull(capturedTransaction.getStartTime());
            System.out.println("断言: 捕获的实体内容验证通过");
        }
    }

    @Nested
    @DisplayName("交易终结")
    class FinalizationTests {

        @Test
        @DisplayName("指定一个 RELAYING 状态的交易, 当更新为最终状态时, 应调用 dao.updateById")
        void givenRelayingTransaction_whenUpdate_thenCallsDaoUpdate() {
            // 准备
            System.out.println("\n--- 测试: (成功场景) 更新RELAYING状态的交易 ---");
            System.out.println("准备: 模拟一个ID为 " + TEST_TRANSACTION_ID + " 且状态为 'RELAYING' 的交易");
            TransactionsEntity relayingTransaction = new TransactionsEntity();
            relayingTransaction.setStatus("RELAYING");
            when(transactionsDao.selectById(TEST_TRANSACTION_ID)).thenReturn(relayingTransaction);
            System.out.println("准备: Mock a ansoactionsDao.selectById() 返回此交易");
            ArgumentCaptor<TransactionsEntity> captor = ArgumentCaptor.forClass(TransactionsEntity.class);

            // 操作
            System.out.println("操作: 调用 updateTransactionAsFinished() 将其状态更新为 'SUCCESS'");
            transactionsService.updateTransactionAsFinished(TEST_TRANSACTION_ID, "SUCCESS", null);

            // 断言
            verify(transactionsDao).updateById(captor.capture());
            System.out.println("断言: 验证 transactionsDao.updateById() 被调用");
            System.out.println("断言: 捕获到的实体状态应为 'SUCCESS', 结束时间不为null");
            assertEquals("SUCCESS", captor.getValue().getStatus());
            assertNotNull(captor.getValue().getEndTime());
            System.out.println("断言: 捕获的实体内容验证通过");
        }

        @Test
        @DisplayName("指定一个已终结的交易(SUCCESS), 当一个延迟的 onClose 尝试将其更新为 INTERRUPTED 时, 应跳过更新")
        void givenFinalizedTransaction_whenUpdateAsInterrupted_thenUpdateIsSkipped() {
            // 准备
            System.out.println("\n--- 测试: (竞争条件) 已终结的交易应跳过后续更新 ---");
            System.out.println("准备: 模拟一个ID为 " + TEST_TRANSACTION_ID + " 且状态为 'SUCCESS' 的交易");
            TransactionsEntity successTransaction = new TransactionsEntity();
            successTransaction.setStatus("SUCCESS");
            when(transactionsDao.selectById(TEST_TRANSACTION_ID)).thenReturn(successTransaction);
            System.out.println("准备: Mock a ansoactionsDao.selectById() 返回此交易");

            // 操作
            System.out.println("操作: (模拟延迟的onClose) 尝试将状态更新为 'INTERRUPTED'");
            transactionsService.updateTransactionAsFinished(TEST_TRANSACTION_ID, "INTERRUPTED", "Connection closed");

            // 断言
            System.out.println("断言: 验证 transactionsDao.updateById() 从未被调用");
            verify(transactionsDao, Mockito.never()).updateById(Mockito.any(TransactionsEntity.class));
            System.out.println("断言: 验证通过");
        }

        @Test
        @DisplayName("指定一个 RELAYING 状态的交易, 当更新为 INTERRUPTED 时, 更新应成功")
        void givenRelayingTransaction_whenUpdatingToInterrupted_thenUpdateSucceeds() {
            // 准备
            System.out.println("\n--- 测试: (失败场景) 更新RELAYING状态的交易为INTERRUPTED ---");
            System.out.println("准备: 模拟一个ID为 " + TEST_TRANSACTION_ID + " 且状态为 'RELAYING' 的交易");
            TransactionsEntity relayingTransaction = new TransactionsEntity();
            relayingTransaction.setStatus("RELAYING");
            when(transactionsDao.selectById(TEST_TRANSACTION_ID)).thenReturn(relayingTransaction);
            System.out.println("准备: Mock a ansoactionsDao.selectById() 返回此交易");
            ArgumentCaptor<TransactionsEntity> captor = ArgumentCaptor.forClass(TransactionsEntity.class);

            // 操作
            System.out.println("操作: 调用 updateTransactionAsFinished() 将其状态更新为 'INTERRUPTED'");
            transactionsService.updateTransactionAsFinished(TEST_TRANSACTION_ID, "INTERRUPTED", "Connection closed");

            // 断言
            verify(transactionsDao).updateById(captor.capture());
            System.out.println("断言: 验证 transactionsDao.updateById() 被调用");
            System.out.println("断言: 捕获到的实体状态应为 'INTERRUPTED'");
            assertEquals("INTERRUPTED", captor.getValue().getStatus());
            System.out.println("断言: 验证通过");
        }
    }
} 