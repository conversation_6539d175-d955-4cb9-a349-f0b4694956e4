<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.nfc.userdevices.dao.UserDevicesDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_user_devices.id,
        t_user_devices.user_id,
        t_user_devices.device_id,
        t_user_devices.device_model,
        t_user_devices.last_login_ip,
        t_user_devices.last_login_location,
        t_user_devices.last_login_at,
        t_user_devices.created_at
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.nfc.userdevices.domain.vo.UserDevicesVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_user_devices
        <where>
            <!--关联的用户ID-->
            <if test="queryForm.userId != null">
                AND t_user_devices.user_id = #{queryForm.userId}
            </if>
            <!--设备型号 (如: 'Google Pixel 8')-->
            <if test="queryForm.deviceModel != null">
                AND t_user_devices.device_model = #{queryForm.deviceModel}
            </if>
        </where>
    </select>


</mapper>
