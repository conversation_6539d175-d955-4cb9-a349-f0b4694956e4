package net.lab1024.sa.admin.module.business.nfc.ws;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.io.IOException;
import java.net.URI;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

public class WebSocketClientEndpoint extends WebSocketClient {

    private final BlockingQueue<String> messages = new LinkedBlockingQueue<>();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public WebSocketClientEndpoint(URI serverUri) {
        super(serverUri);
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        System.out.println("WebSocket Client: Opened connection");
    }

    @Override
    public void onMessage(String message) {
        System.out.println("WebSocket Client: Received message: " + message);
        messages.add(message);
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("WebSocket Client: Closed connection, code=" + code + ", reason=" + reason);
    }

    @Override
    public void onError(Exception ex) {
        System.err.println("WebSocket Client: An error occurred:" + ex);
        ex.printStackTrace();
    }

    /**
     * 等待并获取下一条消息，会阻塞直到收到消息或超时。
     * @param timeoutSeconds 超时时间（秒）
     * @return 收到的JsonNode消息，如果超时则返回null
     * @throws InterruptedException 如果线程在等待时被中断
     * @throws IOException 如果JSON解析失败
     */
    public JsonNode awaitNextMessage(long timeoutSeconds) throws InterruptedException, IOException {
        String message = messages.poll(timeoutSeconds, TimeUnit.SECONDS);
        if (message != null) {
            return objectMapper.readTree(message);
        }
        return null;
    }

    /**
     * 向服务器发送一条文本消息。
     * @param message 要发送的消息字符串
     */
    public void sendMessage(String message) {
        if (isOpen()) {
            send(message);
        } else {
            System.err.println("WebSocket Client: Cannot send message, connection is not open.");
        }
    }
}