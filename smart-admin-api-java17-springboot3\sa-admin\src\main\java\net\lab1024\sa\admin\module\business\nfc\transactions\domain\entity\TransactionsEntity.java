package net.lab1024.sa.admin.module.business.nfc.transactions.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * NFC中继交易记录表 实体类
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:41:36
 * @Copyright xueh所有
 */

@Data
@TableName("t_transactions")
public class TransactionsEntity {

    /**
     * 交易ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 交易唯一标识符
     */
    private String transactionUid;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 读卡端设备信息
     */
    private String transmitterDeviceInfo;

    /**
     * 模拟端设备信息
     */
    private String receiverDeviceInfo;

    /**
     * 交易状态（RELAYING, SUCCESS, FAILED, CANCELLED）
     */
    private String status;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 交易描述/备注 (用户自定义)
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

}
