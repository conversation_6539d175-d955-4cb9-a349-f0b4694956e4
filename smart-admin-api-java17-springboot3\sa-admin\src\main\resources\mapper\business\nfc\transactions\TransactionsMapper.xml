<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.nfc.transactions.dao.TransactionsDao">

    <resultMap id="baseResultMap"
               type="net.lab1024.sa.admin.module.business.nfc.transactions.domain.vo.TransactionsVO">
    </resultMap>

    <select id="queryPage" resultMap="baseResultMap">
        SELECT
            *
        FROM
            t_transactions
        <where>
            <if test="queryForm.userId != null">
                AND user_id = #{queryForm.userId}
            </if>
            <if test="queryForm.deletedFlag != null and !queryForm.deletedFlag">
                AND deleted_flag = 0
            </if>
            <if test="queryForm.deletedFlag != null and queryForm.deletedFlag">
                AND deleted_flag = 1
            </if>
            <if test="queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                INSTR(status,#{queryForm.keyword})
                )
            </if>
            <if test="queryForm.startDate != null and queryForm.endDate == null">
                AND DATE_FORMAT(create_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{queryForm.startDate},'%Y-%m-%d')
            </if>
            <if test="queryForm.endDate != null and queryForm.startDate == null">
                AND DATE_FORMAT(create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{queryForm.endDate},'%Y-%m-%d')
            </if>
            <if test="queryForm.startDate != null and queryForm.endDate != null">
                AND DATE_FORMAT(create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{queryForm.startDate},'%Y-%m-%d') AND
                DATE_FORMAT(#{queryForm.endDate},'%Y-%m-%d')
            </if>
        </where>
        ORDER BY
        id DESC
    </select>

</mapper>
