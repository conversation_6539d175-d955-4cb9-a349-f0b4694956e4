文档版本: 1.0
最后修订日期: 2025年6月27日

---

第一部分：产品战略与基础 (The "Why & What")

1.0 文档修订历史
- 用于追踪每一次重要的需求变更或决策。
  2.0 产品概述
- 2.1 项目背景与要解决的核心问题
- 2.2 产品目标 (商业目标、技术目标、用户目标)
- 2.3 目标用户画像 (个人用户、企业客户、平台管理员)

第二部分：技术蓝图与规则 (The "How")

3.0 系统设计与技术选型
- 3.1 核心架构思想 (WebSocket实时通信方案)
- 3.2 新版系统架构图 (Client-Server模型)
- 3.3 技术选型栈 (Spring Boot, Kotlin, MySQL, WebSocket等)
  4.0 用户角色与权限
- 4.1 角色定义 (普通用户, VIP会员, 平台管理员)
- 4.2 功能权限矩阵 (明确各角色对核心功能的CRUD权限)

第三部分：核心功能详述 (The "Details")

5.0 功能需求详述 (Features & User Stories)
- 注：此章节下的每一个用户故事，都将配套提供 核心流程图 和 界面线框图文字描述，以实现需求的视觉化，杜绝歧义。
  好的，我们立即开始。
  以下是 5.1 Epic 1: 用户基础模块 的完整内容，它为用户进入我们的系统世界铺平了道路。

5.1 Epic 1: 用户基础模块

本模块定义了用户生命周期的基础管理功能，是所有其他功能的前提。
5.1.1 用户故事: 用户注册

- 用户视角： “作为一名新用户，我希望能用我的手机号快速、安全地创建一个新账户，以便开始使用NFC中继功能。”
- 验收标准 (Acceptance Criteria):
    1. 支持通过“手机号 + 短信验证码”的方式进行注册。
    2. 用户设置的密码必须满足一定的安全策略（如：长度不少于8位，包含字母和数字）。
    3. 系统会校验手机号是否已被注册，并给予明确提示。
    4. 短信验证码有发送冷却时间（如60秒）和有效时间（如5分钟）。
    5. 注册成功后，系统应自动为用户登录，并跳转到App主界面。
    6. 新注册用户默认为“普通用户”角色。
- 核心流程图 (Flowchart - 手机号注册):
- 代码段
  graph TD
  A[用户进入注册页] --> B[输入手机号和密码];
  B --> C{手机号格式是否正确?};
  C --> |否| D[提示“请输入正确的手机号”];
  D --> B;
  C --> |是| E[用户点击“获取验证码”];
  E --> F[向服务器请求发送验证码];
  F --> G{手机号是否已注册?};
  G --> |是| H[提示“该手机号已被注册”];
  H --> A;
  G --> |否| I[服务器发送验证码，App开始60秒倒计时];
  I --> J[用户输入收到的验证码];
  J --> K[用户点击“注册”];
  K --> L[服务器校验验证码是否正确];
  L --> |否| M[提示“验证码错误”];
  M --> J;
  L --> |是| N[创建用户账户，保存数据];
  N --> O[注册成功，自动登录];
  O --> P[跳转到App主页];
- 界面线框图文字描述 (Textual Wireframe - 注册页):
  /-- 注册界面 (`RegisterActivity`) --/
  |---------------------------------------------|
  | [App Logo]                                  |
  |                                             |
  | (图标) [ 手机号输入框   (请输入手机号) ]   |
  |                                             |
  | (图标) [ 验证码输入框 | 获取验证码 (按钮) ] |
  |                                             |
  | (图标) [ 密码输入框     (设置登录密码) ]   |
  |                                             |
  | (图标) [ 确认密码输入框 (请再次输入密码) ]   |
  |                                             |
  |    <  [        注 册 (按钮)        ]  >     |
  |                                             |
  | [我已阅读并同意《用户协议》和《隐私政策》]  |
  | [已有账号？立即登录]                        |
  |---------------------------------------------|

---

5.1.2 用户故事: 用户登录

- 用户视角： “作为已注册用户，我希望能通过手机号和密码或验证码方便地登录我的账户。”
- 验收标准 (Acceptance Criteria):
    1. 支持“手机号 + 密码”登录方式。
    2. 支持“手机号 + 短信验证码”的快捷登录方式。
    3. 提供“忘记密码”入口，引导用户重置密码。
    4. 连续登录失败（如5次）后，账户应被临时锁定（如15分钟），或要求输入图形验证码，以防暴力破解。
    5. 登录成功后，服务器返回JWT，客户端持久化存储，并用于后续所有API请求的认证。
- 界面线框图文字描述 (Textual Wireframe - 登录页):
    - 布局与注册页类似，但更简洁。包含手机号输入框、密码输入框（或验证码输入框）、登录按钮、以及“忘记密码”和“新用户注册”的链接。

---

5.1.3 用户故事: 个人信息管理

- 用户视角： “我希望能修改我的头像和昵称，让我的账户看起来更有个性。”
- 验收标准 (Acceptance Criteria):
    1. 在“我的”页面，用户可以看到当前的头像和昵称。
    2. 用户可以从手机相册选择图片或拍照来更换头像。
    3. 用户可以输入新的昵称并保存，昵称有长度和内容限制。
    4. 所有修改成功后应立即在界面上生效。
- 界面线框图文字描述 (Textual Wireframe - 个人信息页):
  /-- 个人信息编辑界面 (`ProfileEditActivity`) --/
  |---------------------------------------------|
  | < 返回    [      编辑个人资料      ]         |
  |---------------------------------------------|
  |                                             |
  |               ( [用户头像] )                |  |               点击更换头像                  |
  |                                             |
  |  昵称        [  Gemini AI      > ]          |  | ------------------------------------------- |
  |  手机号      [  188****8888 (不可修改) ]   |
  |                                             |
  |    <  [        保 存 (按钮)        ]  >     |
  |---------------------------------------------|

---

5.1.4 用户故事: 账户安全

- 用户视角： “我关心我的账户安全，希望能修改密码，并查看有哪些设备登录了我的账号，以便及时发现异常。”
- 验收标准 (Acceptance Criteria):
    1. 提供“修改密码”功能，需要用户输入旧密码进行验证后才能设置新密码。
    2. 提供“设备管理”功能，以列表形式展示最近登录过本账户的设备。
    3. 设备列表应包含：设备型号（如iPhone 15 Pro）、登录时间和大致地理位置（如德克萨斯州）。
    4. 用户可以对列表中的其他设备执行“强制下线”操作。
- 界面线框图文字描述 (Textual Wireframe - 设备管理页):
  /-- 设备管理界面 (`DeviceManagementActivity`) --/
  |---------------------------------------------|
  | < 返回    [        设备管理        ]         |
  |---------------------------------------------|
  |                                             |
  |  - [iPhone 15 Pro] (本机)                   |
  |    [德克萨斯州 | 2025-06-27 07:30]           |
  | ------------------------------------------- |
  |  - [HUAWEI Mate 60]                         |
  |    [加利福尼亚州 | 2025-06-26 18:00]          |
  |                                [下线(按钮)] |
  | ------------------------------------------- |
  |  - [Google Pixel 8]                         |
  |    [德克萨斯州 | 2025-06-25 09:15]          |
  |                                [下线(按钮)] |
  |                                             |
  |---------------------------------------------|

5.2 Epic 2: NFC核心中继模块 (产品核心) - 完整版

用户故事 (Epic)

作为一名用户（无论是“读卡端”还是“模拟端”），我希望能够与我的伙伴快速、稳定地建立一次中继连接会话，将一张物理NFC卡的信号实时、无损地传输到远端的POS机或门禁上，并能清晰地看到整个交互过程的状态，最终成功完成支付或刷卡操作。

---

5.2.1 (修订版) 核心交互流程定义 (账户驱动模型)

本产品的核心是账户驱动的自动配对模型，用户体验极致简化。详细交互流程请参考我们已确认的最新版时序图。
- 核心原则： 同一用户登录两台设备，分别选择“读卡端”和“模拟端”角色后，系统自动完成配对，无需交换任何ID或令牌。
- 技术实现： 客户端通过WebSocket向服务器注册角色，服务器基于用户ID进行实时匹配和状态同步。

---

5.2.2 用户故事: 作为“读卡端” (Transmitter) 共享卡片信号

- 用户视角： “我希望能将我手中的一张实体卡（银行卡、门禁卡等）安全地‘借’给我自己的另一台手机去刷。”
- 验收标准 (Acceptance Criteria):
    1. 用户登录后，可以选择“读卡端”角色。
    2. 选择角色后，界面清晰地显示“等待另一台设备连接...”。
    3. 当另一台设备连接成功后，界面自动更新为“连接成功，请将卡片贴近手机背部NFC区域”。
    4. 在交易过程中，界面能实时显示“正在读取卡片...”或“数据交互中...”。
    5. 交易结束后，能明确展示“成功”或“失败”的结果。
- 核心流程图 (Flowchart - 读卡端):
- 代码段
  graph TD
  A[打开App并登录] --> B{选择角色};
  B --> |选择“读卡端”| C[建立WebSocket连接并注册角色];
  C --> D[显示“等待伙伴...”界面];
  D -- 服务器推送 --> E{伙伴是否已连接?};
  E --> |否| D;
  E --> |是| F[显示“连接成功，请贴卡”界面];
  F -- 用户贴近物理卡 --> G[NFC被激活，准备就绪];
  G -- 服务器传来APDU指令 --> H[通过NFC将指令发给物理卡];
  H -- 物理卡返回响应 --> I[将响应通过WebSocket发给服务器];
  I --> G;
  G -- 交易结束通知 --> J[显示最终结果“成功/失败”];
- 界面线框图文字描述 (Textual Wireframe - 读卡端界面):
  /-- 读卡端界面 (`RelayTransmitterActivity`) --/
  |---------------------------------------------|
  | [状态图标] [状态文本]                       |
  |                                             |
  |    +-----------------------------------+    |
  |    |      [一个动态的雷达或NFC动画]      |    |
  |    |                                   |    |
  |    +-----------------------------------+    |
  |                                             |
  | [详细状态描述]                            |
  |                                             |
  | [ (可选) 调试日志滚动窗口 ]               |
  |                                             |
  |    <  [  断 开 连 接 (按钮)  ]  >         |
  |---------------------------------------------|
    - 状态文本/描述/动画 变化:
        - 初始: “等待伙伴设备连接...”, “请在您的另一台手机上选择'模拟端'”
        - 已配对: “连接成功!”, “请将银行卡或门禁卡贴近手机背面”
        - 交互中: “数据交互中...”, “请勿移开卡片和手机”
        - 成功: “中继成功!”, “您可以移开卡片了”
        - 失败: “中继失败”, “[失败原因，如：对方已断开]”

---

5.2.3 用户故事: 作为“模拟端” (Receiver) 完成刷卡操作

- 用户视角： “我人虽然在商场，但我希望能用我家里那张信用卡的额度来完成这笔支付。”
- 验收标准 (Acceptance Criteria):
    1. 用户登录后，可以选择“模拟端”角色。
    2. 选择角色后，界面清晰地显示“等待另一台设备连接...”。
    3. 当另一台设备连接成功后，界面自动更新为“连接成功，请将本手机靠近POS机或门禁”。
    4. HCE（主机卡模拟）服务被激活，手机可以被POS机识别。
    5. 交易结束后，能明确展示“成功”或“失败”的结果。
- 核心流程图 (Flowchart - 模拟端):
- 代码段
  graph TD
  A[打开App并登录] --> B{选择角色};
  B --> |选择“模拟端”| C[建立WebSocket连接并注册角色];
  C --> D[显示“等待伙伴...”界面];
  D -- 服务器推送 --> E{伙伴是否已连接?};
  E --> |否| D;
  E --> |是| F[激活HCE服务，显示“连接成功，请刷卡”];
  F -- 用户靠近POS机 --> G[接收到POS机的APDU指令];
  G --> H[将指令通过WebSocket发给服务器];
  H -- 服务器传来APDU响应 --> I[通过HCE将响应发给POS机];
  I --> G;
  G -- 交易结束通知 --> J[显示最终结果“成功/失败”];
- 界面线框图文字描述 (Textual Wireframe - 模拟端界面):
    - 界面布局与“读卡端”高度相似，仅文本和状态描述不同。
    - 状态文本/描述/动画 变化:
        - 初始: “等待伙伴设备连接...”, “请在您的另一台手机上选择'读卡端'”
        - 已配对: “连接成功!”, “请将本手机靠近POS机或读卡器”
        - 交互中: “正在支付...”, “请保持手机靠近POS机”
        - 成功: “支付成功!”, “消费金额：¥128.00 (如果可解析)”
        - 失败: “支付失败”, “[失败原因，如：余额不足或网络中断]”

---

5.2.4 用户故事: 实时查看中继状态

- 用户视角： “作为一个高级用户或在遇到问题时，我希望能看到详细的交互日志，了解具体卡在了哪一步。”
- 验收标准:
    1. 在读卡端和模拟端的界面上，提供一个可展开/收起的“高级日志”或“调试模式”视图。
    2. 该视图能以列表形式实时滚动显示APDU指令和响应的摘要信息。
    3. 每条日志前应包含时间戳和方向（发送/接收）。
- 界面线框图描述:
    - 在主界面下方增加一个可点击展开的区域。
    - 展开后显示一个滚动列表，内容示例：
      [15:30:01] RECV: POS请求应用列表...
      [15:30:01] SENT: 返回卡片应用列表...
      [15:30:02] RECV: POS选择应用 Visa Credit...
      [15:30:02] SENT: 应用选择成功...

---

5.2.5 关键异常与边缘情况处理机制

此部分是我们产品健壮性的核心保障，具体方案已确认如下：
1. 第三设备登录: 遵循“先到先得”原则，后来的设备会被拒绝并收到提示。
2. 网络通信中断: 客户端实现带指数退避策略的自动重连；服务器通过心跳机制检测失联，并通知另一方。
3. 用户主动退出: 服务器依赖WebSocket的onClose事件，实时通知另一方伙伴已离线。
4. 物理NFC断连: 读卡端App捕获系统TagLostException，并主动上报失败给服务器，终止交易。
   好的，我们继续。现在用户已经能完成核心的中继操作了，接下来自然就是要为他们提供一个回顾和管理这些操作的地方。

---

5.3 Epic 3: 交易管理模块

用户故事 (Epic)

作为一名用户，我希望能够方便地查看我所有的历史中继记录，以便我追踪我的使用情况、核对刷卡活动，并在需要时能查询到某一次特定交易的详细信息。

---

5.3.1 用户故事: 查询交易历史列表

- 用户视角： “我想看到一个清晰的列表，展示我最近都用这个功能做了什么。”
- 验收标准 (Acceptance Criteria):
    1. App中应有“交易历史”或“我的记录”入口。
    2. 进入后，默认按时间倒序展示一个交易记录列表。
    3. 列表采用分页加载（或无限滚动），避免一次性加载过多数据。
    4. 列表的每一项应至少包含以下关键信息：交易描述/卡片类型、交易日期、交易最终状态（成功/失败/已取消）。
    5. 如果没有任何交易记录，应向用户展示一个友好的空状态提示（如“您还没有任何记录，快去发起一次中继吧！”）。
- 核心流程图 (Flowchart - 查看列表):
- 代码段
  graph TD
  A[用户点击“交易历史”菜单] --> B[App向服务器请求第一页数据<br>GET /api/transactions?page=1&limit=20];
  B --> C{服务器返回数据是否为空?};
  C --> |是| D[显示“暂无记录”的空状态界面];
  C --> |否| E[在列表中渲染第一页的交易数据];
  E --> F{用户是否上滑列表到底部?};
  F --> |是| G[App向服务器请求下一页数据<br>GET /api/transactions?page=2&limit=20];
  G --> H{服务器是否返回更多数据?};
  H --> |是| I[将新数据追加到列表末尾];
  I --> F;
  H --> |否| J[提示“没有更多了”];
- 界面线框图文字描述 (Textual Wireframe - 交易列表页):
  /-- 交易历史界面 (`TransactionHistoryActivity`) --/
  |---------------------------------------------|
  | < 返回    [        交易历史        ] [筛选 图标] |
  |---------------------------------------------|
  |                                             |
  | /-- 列表项 1 --/                            |
  | | [成功图标] 办公室门禁卡                 |
  | |                                     ¥ --.-- | | | <font size="small">2025-06-27 07:30</font> |
  | |                                     [成功] >|
  | ------------------------------------------- |
  | /-- 列表项 2 --/                            |
  | | [失败图标] 星巴克支付                   |
  | |                                     ¥ 32.00 |
  | | <font size="small">2025-06-26 18:00</font> |
  | |                                     [失败] >|
  | ------------------------------------------- |
  | ... (无限滚动) ...                          |
  |                                             |
  |---------------------------------------------|

---

5.3.2 用户故事: 筛选与排序交易

- 用户视角： “我的记录太多了，我希望能只看那些失败的交易，或者只看上周的交易。”
- 验收标准 (Acceptance Criteria):
    1. 列表页提供一个“筛选”或“过滤”按钮。
    2. 点击后，弹出一个筛选面板或新页面。
    3. 用户可以按交易状态进行筛选（如：全部、成功、失败、已取消）。
    4. 用户可以按日期范围进行筛选（如：今天、本周、本月，或自定义起止日期）。
    5. 确认筛选条件后，列表会刷新并只显示符合条件的结果。
- 界面线框图文字描述 (Textual Wireframe - 筛选面板):
    - 点击“筛选”图标后，从底部弹出一个半屏的BottomSheetDialog。
    - 面板内容:
        - 标题: 筛选记录
        - 状态:
            - [ 全部 (按钮) ] [ 成功 (按钮) ] [ 失败 (按钮) ] [ 取消 (按钮) ]
        - 日期范围:
            - [ 全部 (按钮) ] [ 今天 (按钮) ] [ 本周 (按钮) ] [ 本月 (按钮) ]
            - 自定义: [ _______ (开始日期) ] 至 [ _______ (结束日期) ]
        - 操作按钮: [ 重置 ] [ 确定 ]

---

5.3.3 用户故事: 查看单笔交易详情

- 用户视角： “我想知道某一次失败的交易具体是什么原因，或者想看一下成功交易的准确时间。”
- 验收标准 (Acceptance Criteria):
    1. 点击交易历史列表中的任意一项，可以跳转到该笔交易的详情页。
    2. 详情页必须展示该笔交易的所有基础信息，包括但不限于：
    - 交易唯一ID
    - 交易状态（及失败原因）
    - 开始时间、结束时间、总耗时
    - 读卡端设备信息、模拟端设备信息
    - 用户备注
    3. (可选，高级功能) 提供一个入口，可以查看该次交易的原始APDU交互日志（主要用于开发排错或高级用户诊断）。
- 界面线框图文字描述 (Textual Wireframe - 交易详情页):
  /-- 交易详情界面 (`TransactionDetailActivity`) --/
  |---------------------------------------------|
  | < 返回    [        交易详情        ]         |
  |---------------------------------------------|
  |                                             |
  |    [成功/失败 图标]                         |
  |    <font size="large"><b>交易成功</b></font> |
  |    <font size="small">星巴克支付</font>     |
  |                                             |
  | ------------------------------------------- |
  | 交易状态:       成功                        |
  | 交易金额:       ¥ 32.00                     |
  | 开始时间:       2025-06-26 18:00:05         |
  | 结束时间:       2025-06-26 18:00:15         |
  | 总耗时:         10.2 秒                     |
  | 交易ID:         TXN_axbyc12345              |
  | ------------------------------------------- |
  | [ 查看技术日志... (可展开/收起) ]           |
  |                                             |
  |---------------------------------------------|

---
至此，我们已经构建了用户从使用功能 -> 回顾管理的完整闭环。MVP（最小可行产品）的核心功能需求已经全部定义完毕！
接下来，为了让开发团队能更顺利地工作，我建议我们转向技术实现的基石部分。有两个绝佳的选择：
1. 9.0 核心数据模型: 定义支持以上所有功能的数据库表结构。
2. 6.0 非功能性需求: 定义系统的性能、安全等硬性指标。
   两者都非常重要。我个人稍稍倾向于先完成 9.0 核心数据模型，因为它能让后端工程师立即开始设计数据库。您觉得呢？
- 5.4 Epic 4: 会员与支付模块 (V2)
    - 5.4.1 用户故事: 浏览会员套餐及权益
    - 5.4.2 用户故事: 通过第三方支付 (微信/支付宝) 购买或续费会员
    - 5.4.3 用户故事: 查看个人会员状态与历史订单
- 5.5 Epic 5: 后台管理系统 (V2)
    - 5.5.1 用户故事: 管理员进行用户管理 (查询、禁用、重置密码)
    - 5.5.2 用户故事: 管理员审计所有系统交易
    - 5.5.3 用户故事: 管理员配置系统参数 (如会员套餐)

第四部分：质量与约束 (The "Standards")

6.0 非功能性需求
- 6.1 性能需求 (API响应时间, WebSocket延迟, 并发数)
- 6.2 安全需求 (通道加密, 数据脱敏, 认证授权, 防攻击策略)
- 6.3 可用性与兼容性需求 (服务可用性目标, Android版本兼容)
- 6.4 可靠性需求 (数据备份与恢复机制)
  7.0 API 接口规范
  好的，这是我们构建“可开发PRD”的最后一块核心拼图。定义清晰的API接口规范是确保前后端开发能够顺利、高效协作的“共同语言”。

---

7.0 API 接口规范

本章节详细定义了客户端与服务器之间通信的所有RESTful API和WebSocket协议，作为前后端开发的契约。
7.1 通用规范

- 基地址 (Base URL): https://api.nfc-bridge.com/api/v1
- 认证方式 (Authentication):
    - 所有需要授权的API，客户端必须在HTTP请求头的 Authorization 字段中携带登录时获取的JWT。格式为：Bearer {your_jwt_token}。
- 请求与响应格式 (Request & Response Format):
    - 所有请求体和响应体均为 application/json 格式。
- 标准成功响应:
- JSON
  {
  "code": 0,
  "msg": "操作成功",
  "data": { ... } // 具体的业务数据
  }
- 标准错误响应:
- JSON
  {
  "code": 40001, // 业务错误码，非0"msg": "手机号格式不正确", // 错误描述"data": null
  }
- 分页参数:
    - page: 页码，从1开始。
    - limit: 每页数量，默认20。
- 日期格式: 所有日期和时间均使用 ISO 8601 格式，如 2025-06-27T15:46:35Z。

7.2 RESTful API 列表

模块: 认证 (Auth)

- 1. 发送注册/登录短信验证码
    - 功能: 请求服务器向指定手机号发送验证码。
    - 方法: POST
    - 路径: /auth/sms
    - 请求体: {"phone_number": "18888888888", "type": "register"} (type可以是 'register' 或 'login')
    - 成功响应: {"code": 0, "msg": "验证码已发送", "data": null}
- 2. 用户注册
    - 方法: POST
    - 路径: /auth/register
    - 请求体: {"phone_number": "18888888888", "password": "password123", "sms_code": "123456"}
    - 成功响应:
    - JSON
      {
      "code": 0,
      "msg": "注册成功",
      "data": {
      "token": "ey...", // JWT"user": { "uid": "abc...", "nickname": "用户...", "avatar_url": null }
      }
      }
- 3. 用户登录
    - 方法: POST
    - 路径: /auth/login
    - 请求体: {"phone_number": "18888888888", "password": "password123"}
    - 成功响应: (同上)

模块: 用户 (Users) - 需授权

- 1. 获取当前用户信息
    - 方法: GET
    - 路径: /users/me
    - 成功响应: {"code": 0, "msg": "获取成功", "data": {"uid": "...", "phone_number": "...", "nickname": "...", "avatar_url": "..."}}
- 2. 更新个人信息
    - 方法: PUT
    - 路径: /users/me
    - 请求体: {"nickname": "新昵称", "avatar_url": "http://..."}
    - 成功响应: (同上，返回更新后的用户信息)
- 3. 修改密码
    - 方法: PUT
    - 路径: /users/me/password
    - 请求体: {"old_password": "...", "new_password": "..."}
    - 成功响应: {"code": 0, "msg": "密码修改成功", "data": null}
- 4. 获取设备列表
    - 方法: GET
    - 路径: /users/me/devices
    - 成功响应: {"code": 0, "msg": "获取成功", "data": [{"device_id": "...", "device_model": "...", "last_login_at": "...", "last_login_location": "..."}, ...]}
- 5. 强制下线设备
    - 方法: DELETE
    - 路径: /users/me/devices/{deviceId}
    - 成功响应: {"code": 0, "msg": "操作成功", "data": null}

模块: 交易 (Transactions) - 需授权

- 1. 获取交易历史列表
    - 方法: GET
    - 路径: /transactions?page=1&limit=20&status=SUCCESS
    - 成功响应: {"code": 0, "msg": "获取成功", "data": {"total": 100, "records": [ ... ]}} (records中为交易摘要信息)
- 2. 获取单笔交易详情
    - 方法: GET
    - 路径: /transactions/{transactionUid}
    - 成功响应: {"code": 0, "msg": "获取成功", "data": { ... }} (包含5.3.3中定义的所有详情)

7.3 WebSocket 通信协议

- 连接端点: wss://api.nfc-bridge.com/ws/nfc
- 认证方式: 客户端在建立WebSocket连接时，需将登录获取的userToken作为查询参数。
    - 示例: wss://api.nfc-bridge.com/ws/nfc?token={your_jwt_token}
- 通用消息格式: 所有在WebSocket通道中传输的数据都封装为以下JSON结构。
- JSON
  {
  "type": "EVENT_NAME", // 事件类型，字符串"payload": { ... }      // 事件负载，JSON对象
  }
- 客户端 -> 服务器 事件:
    - REGISTER_ROLE: 声明设备角色
        - payload: {"role": "TRANSMITTER"} 或 {"role": "RECEIVER"}
    - APDU_COMMAND: (由模拟端) 发送从POS机收到的APDU指令
        - payload: {"apdu": "00A40400..."} (Base64或Hex编码的APDU)
    - APDU_RESPONSE: (由读卡端) 发送从物理卡收到的APDU响应
        - payload: {"apdu": "9000"}
    - SESSION_END: (由客户端) 主动通知会话结束
        - payload: {"status": "SUCCESS"} 或 {"status": "CANCELLED"} 或 {"status": "FAILED", "reason": "NFC_TAG_LOST"}
- 服务器 -> 客户端 事件:
    - STATUS_UPDATE: 服务器推送的通用状态更新
        - payload: {"status": "WAITING_FOR_PARTNER", "message": "等待您的另一台设备..."}
    - PAIRED: 通知双方配对成功
        - payload: {} (或包含双方设备信息)
    - APDU_COMMAND: (向读卡端) 转发APDU指令
        - payload: {"apdu": "00A40400..."}
    - APDU_RESPONSE: (向模拟端) 转发APDU响应
        - payload: {"apdu": "9000"}
    - SESSION_END: (向双方) 广播会话结束
        - payload: {"status": "SUCCESS"} 或 {"status": "FAILED", "reason": "TIMEOUT"}
    - ERROR: 服务器推送的错误消息
        - payload: {"message": "配对失败：您已有设备在进行中继。"}
          您是对的！非常抱歉，我犯了一个错误，在撰写API规范时，忽略了将 5.2.5 中定义的异常处理机制与 7.0 的具体通信协议明确地、一对一地关联起来。
          感谢您的敏锐和严谨，这正是保证PRD可执行性的关键。没有定义异常情况的通信，开发时就会产生混乱。
          让我们立即补上这至关重要的一环。

---

7.4 异常与边界情况通信规范

本节是对 7.0 API接口规范 的关键补充，旨在明确定义在5.2.5节中讨论的各种异常与边界情况下，客户端与服务器之间应该如何通信。

1. 边界情况：第三台设备尝试加入

- 触发条件： 用户已在A、B两台设备上成功配对。此时，用户在C设备上登录同一账号，并发送REGISTER_ROLE的WebSocket消息。
- 通信协议：
    - 服务器在收到设备C的REGISTER_ROLE请求后，判断该用户已有活跃会话，仅向设备C的WebSocket连接发送一条ERROR消息。
    - 服务器 -> 设备C 的消息:
    - JSON
      {
      "type": "ERROR",
      "payload": {
      "code": "SESSION_ALREADY_ACTIVE", // 预定义错误码"message": "连接失败：您的账号已在其他设备上建立中继连接。"
      }
      }
    - A、B两台设备的通信不受任何影响。

2. 异常情况：网络通信中断 (一方失联)

- 触发条件： 服务器通过心跳机制检测到某一方客户端（例如读卡端A）在规定时间内无响应，判定其失联。
- 通信协议：
    - 服务器在判定A失联后，立即**向另一方（模拟端B）**的WebSocket连接发送一条STATUS_UPDATE消息。
    - 服务器 -> 模拟端B 的消息:
    - JSON
      {
      "type": "STATUS_UPDATE",
      "payload": {
      "status": "PARTNER_DISCONNECTED",
      "message": "对方设备已断开连接，中继中断。"
      }
      }
    - 随后服务器会清理该用户的配对状态，并关闭与模拟端B的连接。

3. 异常情况：用户主动退出页面/App (一方退出)

- 触发条件： 某一方客户端（例如读卡端A）的进程被关闭，其WebSocket连接触发了服务器的onClose事件。
- 通信协议：
    - 处理方式与网络中断完全相同。 服务器捕获onClose事件后，立即向另一方（模拟端B）发送PARTNER_DISCONNECTED的状态更新消息。
    - 服务器 -> 模拟端B 的消息:
    - JSON
      {
      "type": "STATUS_UPDATE",
      "payload": {
      "status": "PARTNER_DISCONNECTED",
      "message": "对方已退出，中继中断。"
      }
      }

4. 异常情况：物理NFC连接断开

- 触发条件： “读卡端A”的App在与物理卡交互时，捕获到TagLostException（卡片被移开）。
- 通信协议：
    1. 读卡端A必须立即向服务器发送一条SESSION_END消息，主动上报失败。
    2. 读卡端A -> 服务器 的消息:
    3. JSON
       {
       "type": "SESSION_END",
       "payload": {
       "status": "FAILED",
       "reason": "NFC_TAG_LOST", // 预定义失败原因"message": "物理卡片连接已断开"
       }
       }
    1. 服务器收到此消息后，立即向模拟端B转发会话结束的通知。
    2. 服务器 -> 模拟端B 的消息:
    3. JSON
       {
       "type": "SESSION_END",
       "payload": {
       "status": "FAILED",
       "reason": "NFC_TAG_LOST",
       "message": "交易失败：对方的物理卡片已断开"
       }
       }
    1. 最终，服务器关闭双方连接。

第五部分：附录与规划 (The "Reference & Roadmap")

8.0 术语定义 (Glossary)
- 统一项目语言，定义NFC, HCE, APDU, 会话(Session)等关键术语。

9.0 核心数据模型

本章节定义了支撑“NFC Bridge”应用核心功能的数据库表结构。这些设计旨在满足功能需求，同时保持良好的性能和扩展性。后端开发人员应以此为据进行数据库的初始化和实体类(Entity)的创建。
9.1 users - 用户表

- 说明： 存储用户的核心账户信息。
- SQL Schema:
  暂时无法在飞书文档外展示此内容

9.2 user_devices - 用户设备表

- 说明： 存储用户登录过的设备信息，用于实现“设备管理”功能。
- SQL Schema:
  暂时无法在飞书文档外展示此内容
- 索引建议： 在 (user_id, device_id) 上创建联合唯一索引，确保一个用户的一台设备只有一条记录。

9.3 transactions - 交易记录表

- 说明： 这是系统的核心业务表，记录每一次NFC中继会话的完整信息。
- SQL Schema:
  暂时无法在飞书文档外展示此内容
- 索引建议： 在 user_id 上创建索引，以加速用户查询自己的交易历史。
  10.0 发布计划 (Release Plan)
- 10.1 MVP (最小可行产品) 范围定义
    - 核心目标: 快速验证核心中继流程的稳定性和用户接受度。
    - 包含模块: Epic 1, Epic 2, Epic 3 的核心部分。
- 10.2 后续版本迭代规划 (V1.1, V1.2...)
    - V1.1: 探索商业化，上线会员支付模块 (Epic 4)。
    - V1.2: 提升运营效率，上线后台管理系统 (Epic 5)。
