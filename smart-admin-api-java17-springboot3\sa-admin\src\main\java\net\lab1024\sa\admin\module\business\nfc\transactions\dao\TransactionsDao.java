package net.lab1024.sa.admin.module.business.nfc.transactions.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.entity.TransactionsEntity;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.form.TransactionsQueryForm;
import net.lab1024.sa.admin.module.business.nfc.transactions.domain.vo.TransactionsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * NFC中继交易记录表 Dao
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:41:36
 * @Copyright xueh所有
 */

@Mapper
public interface TransactionsDao extends BaseMapper<TransactionsEntity> {

    /**
     * 分页查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<TransactionsVO> queryPage(Page page, @Param("queryForm") TransactionsQueryForm queryForm);

}
