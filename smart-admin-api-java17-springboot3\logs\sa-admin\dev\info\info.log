[2025-06-30 00:00:39,837][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:01:39,867][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:02:02,367][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-30 00:02:39,893][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:03:39,923][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:04:02,697][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 00:04:39,952][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:05:39,990][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:06:03,032][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 00:06:40,017][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:07:40,044][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:08:03,393][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 00:08:40,067][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:09:40,108][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:10:03,732][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->107ms 
[2025-06-30 00:10:40,137][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:11:40,168][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:12:04,083][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-30 00:12:40,198][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:13:40,220][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:14:04,428][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 00:14:40,245][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:15:10,197][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 00:15:40,264][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:16:04,755][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 00:16:40,297][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:17:40,323][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:18:05,110][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 00:18:40,350][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:19:40,380][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:20:05,433][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 00:20:40,403][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:21:40,450][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:22:06,032][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 00:22:40,473][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:23:40,502][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:24:06,365][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 00:24:40,531][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:25:40,573][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:26:06,760][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 00:26:40,604][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:27:40,631][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:28:07,087][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 00:28:40,660][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:29:40,700][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:30:07,396][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 00:30:40,717][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:31:40,755][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:32:07,726][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 00:32:40,779][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:33:40,806][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:34:08,050][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 00:34:40,825][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:35:40,859][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:36:08,369][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 00:36:40,887][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:37:40,913][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:38:08,694][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 00:38:40,941][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:39:40,976][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:40:09,036][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 00:40:41,007][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:41:41,039][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:42:09,373][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 00:42:41,066][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:43:41,093][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:44:09,710][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 00:44:41,119][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:45:41,164][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:46:10,436][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->261ms 
[2025-06-30 00:46:41,180][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:47:41,210][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:48:10,799][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 00:48:41,227][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:49:41,264][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:50:41,292][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:51:41,338][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:52:11,180][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 00:52:41,360][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:53:41,385][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:54:11,537][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 00:54:41,407][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:55:41,445][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:56:11,904][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 00:56:41,472][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:57:41,497][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:58:41,529][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 00:59:41,576][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:00:12,294][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 01:00:41,604][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:01:41,636][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:02:12,609][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 01:02:41,657][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:03:41,675][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:04:12,927][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->94ms 
[2025-06-30 01:04:41,700][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:05:41,744][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:06:13,253][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 01:06:41,772][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:07:41,799][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:08:13,579][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 01:08:41,817][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:09:41,861][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:10:13,920][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->123ms 
[2025-06-30 01:10:41,876][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:11:41,919][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:12:41,943][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:13:41,966][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:14:14,310][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-30 01:14:42,000][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:15:10,193][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 01:15:42,022][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:16:14,632][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 01:16:42,049][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:17:42,075][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:18:15,311][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->264ms 
[2025-06-30 01:18:42,096][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:19:42,130][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:20:15,664][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 01:20:42,150][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:21:42,200][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:22:16,011][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 01:22:42,228][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:23:42,253][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:24:16,328][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-30 01:24:42,282][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:25:42,328][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:26:16,655][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-30 01:26:42,344][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:27:42,367][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:28:16,968][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-30 01:28:42,390][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:29:42,430][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:30:17,287][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-30 01:30:42,443][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:31:42,478][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:32:17,634][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-30 01:32:42,500][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:33:42,520][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:34:17,975][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-30 01:34:42,543][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:35:42,578][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:36:18,322][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 01:36:42,600][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:37:42,616][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:38:18,647][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-30 01:38:42,659][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:39:42,700][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:40:18,977][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 01:40:42,729][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:41:42,770][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:42:19,303][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 01:42:42,794][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:43:42,819][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:44:19,618][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 01:44:42,850][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:45:42,894][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:46:19,938][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 01:46:42,916][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:47:42,935][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:48:20,279][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->110ms 
[2025-06-30 01:48:42,956][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:48:56,670][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-30 01:48:56,696][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 25252 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-30 01:48:56,697][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-30 01:48:57,348][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-30 01:48:57,348][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-30 01:48:57,369][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces. 
[2025-06-30 01:48:58,220][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-30 01:48:58,972][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-30 01:48:58,979][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-30 01:48:58,982][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-30 01:48:58,982][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-30 01:48:59,027][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-30 01:48:59,027][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 2309 ms 
[2025-06-30 01:48:59,474][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-30 01:48:59,759][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-30 01:48:59,991][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 01:49:00,666][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 01:49:01,545][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-30 01:49:01,827][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-30 01:49:02,498][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-30 01:49:02,540][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-30 01:49:02,546][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-30 01:49:02,571][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 6.214 seconds (process running for 6.67) 
[2025-06-30 01:49:02,740][INFO ][RMI TCP Connection(2)-192.168.50.194][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-30 01:49:02,740][INFO ][RMI TCP Connection(2)-192.168.50.194][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-30 01:49:02,741][INFO ][RMI TCP Connection(2)-192.168.50.194][o.s.w.s.DispatcherServlet:554] Completed initialization in 1 ms 
[2025-06-30 01:49:11,560][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-30 01:49:11,560][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-30 01:49:11,562][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:50:11,594][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:51:11,632][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:51:11,984][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->114ms 
[2025-06-30 01:52:11,667][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:53:11,714][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:53:12,436][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->110ms 
[2025-06-30 01:54:11,732][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:55:11,764][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:55:12,768][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 01:56:11,789][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:57:11,833][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:58:11,864][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:59:11,884][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 01:59:13,174][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->108ms 
[2025-06-30 02:00:11,910][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:01:11,943][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:01:13,507][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 02:02:11,963][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:03:11,997][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:03:13,836][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 02:04:12,027][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:05:12,049][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:06:12,071][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:07:12,105][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:07:14,238][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 02:08:12,132][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:09:12,266][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:09:14,590][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->110ms 
[2025-06-30 02:10:12,297][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:11:12,330][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:11:15,248][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->220ms 
[2025-06-30 02:12:12,354][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:13:12,390][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:13:15,610][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 02:14:12,419][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:15:10,200][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 02:15:12,446][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:15:15,941][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 02:16:12,471][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:17:12,516][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:17:16,272][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-30 02:18:12,537][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:19:12,566][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:19:16,614][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 02:20:12,590][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:21:12,623][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:21:16,936][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 02:22:12,641][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:23:12,683][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:23:17,280][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-30 02:24:12,700][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:25:12,726][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:25:17,620][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-30 02:26:12,747][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:27:12,778][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:27:17,936][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 02:28:12,813][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:29:12,841][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:29:18,271][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 02:30:12,874][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:31:12,910][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:31:18,605][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 02:32:12,931][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:33:12,963][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:33:18,931][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 02:34:12,980][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:35:13,004][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:35:19,267][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 02:36:13,020][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:37:13,066][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:37:19,601][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-30 02:38:13,087][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:39:13,113][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:39:19,936][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-30 02:40:13,131][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:41:13,175][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:41:20,267][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 02:42:13,199][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:43:13,240][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:43:20,627][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 02:44:13,260][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:45:13,288][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:45:20,970][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->108ms 
[2025-06-30 02:46:13,313][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:47:13,346][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:47:21,299][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 02:48:13,369][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:49:13,395][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:49:21,635][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->108ms 
[2025-06-30 02:50:13,418][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:51:13,458][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:51:21,976][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-30 02:52:13,475][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:53:13,514][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:53:22,294][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-30 02:54:13,537][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:55:13,568][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:55:22,639][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->104ms 
[2025-06-30 02:56:13,592][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:57:13,628][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:57:22,973][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 02:58:13,650][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:59:13,698][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 02:59:23,291][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 02:59:34,452][INFO ][http-nio-1024-exec-4][o.s.a.AbstractOpenApiResource:390] Init duration for springdoc-openapi is: 703 ms 
[2025-06-30 03:00:13,718][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:01:13,758][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:01:23,642][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 03:02:13,776][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:03:13,807][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:03:23,970][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 03:04:13,833][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:05:13,857][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:05:24,300][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 03:06:13,873][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:07:13,910][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:07:24,714][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->138ms 
[2025-06-30 03:08:13,926][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:09:13,944][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:09:25,043][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 03:10:13,972][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:11:14,016][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:11:25,378][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->103ms 
[2025-06-30 03:12:14,044][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:14:05,877][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:15:05,906][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:15:10,184][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 03:15:35,233][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 03:16:05,924][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:17:05,960][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:17:35,540][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-30 03:18:05,986][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:19:06,028][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:19:35,844][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 03:20:06,052][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:21:06,090][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:22:06,114][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:23:06,157][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:23:36,247][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-30 03:24:06,185][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:25:06,209][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 03:25:36,559][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-30 03:26:06,237][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:09:48,901][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-30 04:09:48,925][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 13608 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-30 04:09:48,926][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-30 04:09:49,585][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-30 04:09:49,587][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-30 04:09:49,606][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces. 
[2025-06-30 04:09:50,381][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-30 04:09:51,231][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-30 04:09:51,238][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-30 04:09:51,240][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-30 04:09:51,240][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-30 04:09:51,294][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-30 04:09:51,295][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 2346 ms 
[2025-06-30 04:09:51,771][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-30 04:09:52,070][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-30 04:09:52,311][INFO ][redisson-netty-2-8][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 04:09:52,952][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 04:09:53,841][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-30 04:09:54,141][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-30 04:09:54,841][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-30 04:09:54,883][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-30 04:09:54,902][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-30 04:09:54,928][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 6.353 seconds (process running for 6.852) 
[2025-06-30 04:09:55,414][INFO ][RMI TCP Connection(4)-192.168.50.6][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-30 04:09:55,414][INFO ][RMI TCP Connection(4)-192.168.50.6][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-30 04:09:55,415][INFO ][RMI TCP Connection(4)-192.168.50.6][o.s.w.s.DispatcherServlet:554] Completed initialization in 1 ms 
[2025-06-30 04:10:03,836][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-30 04:10:03,836][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-30 04:10:03,837][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:10:04,179][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->86ms 
[2025-06-30 04:11:03,864][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:12:03,883][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:12:04,482][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->84ms 
[2025-06-30 04:13:03,910][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:14:03,950][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:14:04,772][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->82ms 
[2025-06-30 04:15:03,965][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:15:10,168][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 04:16:03,989][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:17:04,014][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:18:04,051][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:18:05,128][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->81ms 
[2025-06-30 04:19:04,068][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:20:04,085][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:20:05,433][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->84ms 
[2025-06-30 04:21:04,109][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:22:04,143][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:22:05,738][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->84ms 
[2025-06-30 04:23:04,171][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:24:04,201][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:25:04,226][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:26:04,241][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:26:06,110][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->81ms 
[2025-06-30 04:27:04,262][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:28:04,302][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:28:06,415][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->86ms 
[2025-06-30 04:29:04,331][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:30:04,349][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:30:06,716][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->85ms 
[2025-06-30 04:31:04,368][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:32:04,398][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:32:07,008][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->86ms 
[2025-06-30 04:33:04,417][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:34:04,454][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:34:07,301][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->82ms 
[2025-06-30 04:35:04,474][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:36:04,497][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:36:07,608][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->83ms 
[2025-06-30 04:37:04,522][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:38:04,537][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:38:07,951][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->86ms 
[2025-06-30 04:39:04,555][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:40:04,571][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:40:08,256][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->84ms 
[2025-06-30 04:41:04,588][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:42:04,614][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:42:08,545][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->83ms 
[2025-06-30 04:43:04,636][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:44:04,663][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:44:08,853][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->85ms 
[2025-06-30 04:45:00,338][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-30 04:45:00,364][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 14104 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-30 04:45:00,367][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-30 04:45:01,079][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-30 04:45:01,079][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-30 04:45:01,105][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces. 
[2025-06-30 04:45:01,948][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-30 04:45:02,948][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-30 04:45:02,956][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-30 04:45:02,957][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-30 04:45:02,959][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-30 04:45:03,013][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-30 04:45:03,013][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 2621 ms 
[2025-06-30 04:45:03,495][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-30 04:45:03,797][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-30 04:45:04,057][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 04:45:04,746][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 04:45:05,616][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-30 04:45:05,930][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-30 04:45:06,636][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-30 04:45:06,688][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-30 04:45:06,693][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-30 04:45:06,723][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 6.768 seconds (process running for 7.383) 
[2025-06-30 04:45:07,143][INFO ][RMI TCP Connection(2)-192.168.50.6][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-30 04:45:07,143][INFO ][RMI TCP Connection(2)-192.168.50.6][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-30 04:45:07,145][INFO ][RMI TCP Connection(2)-192.168.50.6][o.s.w.s.DispatcherServlet:554] Completed initialization in 2 ms 
[2025-06-30 04:45:15,615][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-30 04:45:15,616][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-30 04:45:15,616][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:46:15,648][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:47:15,685][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:47:16,022][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 04:48:15,710][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:49:15,750][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:49:16,335][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 04:50:15,775][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:51:15,801][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:51:16,645][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 04:52:15,819][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:53:15,862][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:53:16,965][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 04:54:15,884][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:55:15,907][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:55:17,292][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-30 04:56:15,935][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:56:53,630][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-30 04:56:53,659][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-30 04:56:53,662][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-30 04:57:41,349][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-30 04:57:41,377][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 32868 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-30 04:57:41,378][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-30 04:57:42,089][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-30 04:57:42,090][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-30 04:57:42,113][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces. 
[2025-06-30 04:57:42,993][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-30 04:57:44,041][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-30 04:57:44,049][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-30 04:57:44,050][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-30 04:57:44,051][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-30 04:57:44,104][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-30 04:57:44,104][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 2696 ms 
[2025-06-30 04:57:44,602][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-30 04:57:44,901][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-30 04:57:45,158][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 04:57:46,068][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 04:57:46,944][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-30 04:57:47,278][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-30 04:57:48,041][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-30 04:57:48,090][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-30 04:57:48,095][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-30 04:57:48,125][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 7.226 seconds (process running for 7.824) 
[2025-06-30 04:57:48,581][INFO ][RMI TCP Connection(2)-192.168.50.6][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-30 04:57:48,582][INFO ][RMI TCP Connection(2)-192.168.50.6][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-30 04:57:48,583][INFO ][RMI TCP Connection(2)-192.168.50.6][o.s.w.s.DispatcherServlet:554] Completed initialization in 1 ms 
[2025-06-30 04:57:56,940][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-30 04:57:56,942][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-30 04:57:56,942][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:57:57,301][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-30 04:58:56,972][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:59:58,039][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 04:59:58,291][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-30 05:00:58,095][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:01:58,139][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:01:58,648][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->98ms 
[2025-06-30 05:02:58,169][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:03:58,193][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:03:59,004][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-30 05:04:58,218][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:05:58,256][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:05:59,358][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-30 05:06:58,272][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:07:58,290][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:07:59,719][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-30 05:08:58,315][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:09:58,362][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:10:00,042][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 05:10:58,382][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:11:42,532][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-30 05:11:42,559][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-30 05:11:42,561][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-30 05:11:47,506][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-30 05:11:47,537][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 8720 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-30 05:11:47,540][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-30 05:11:48,388][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-30 05:11:48,390][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-30 05:11:48,416][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces. 
[2025-06-30 05:11:49,348][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-30 05:11:50,356][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-30 05:11:50,363][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-30 05:11:50,364][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-30 05:11:50,364][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-30 05:11:50,419][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-30 05:11:50,419][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 2831 ms 
[2025-06-30 05:11:50,879][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-30 05:11:51,174][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-30 05:11:51,439][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 05:11:52,065][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 05:11:52,951][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-30 05:11:53,261][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-30 05:11:54,000][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-30 05:11:54,047][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-30 05:11:54,052][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-30 05:11:54,080][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 7.0 seconds (process running for 7.715) 
[2025-06-30 05:11:54,282][INFO ][RMI TCP Connection(3)-192.168.50.6][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-30 05:11:54,282][INFO ][RMI TCP Connection(3)-192.168.50.6][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-30 05:11:54,285][INFO ][RMI TCP Connection(3)-192.168.50.6][o.s.w.s.DispatcherServlet:554] Completed initialization in 2 ms 
[2025-06-30 05:12:02,962][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-30 05:12:02,962][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-30 05:12:02,962][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:12:03,270][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->77ms 
[2025-06-30 05:13:02,992][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:14:03,030][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:14:03,548][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->81ms 
[2025-06-30 05:15:03,060][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:15:10,180][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 05:16:03,080][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:16:03,842][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->88ms 
[2025-06-30 05:17:03,122][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:18:03,152][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:19:03,168][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:20:03,198][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:20:04,233][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->83ms 
[2025-06-30 05:21:03,226][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:22:03,255][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:22:04,501][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->78ms 
[2025-06-30 05:23:03,273][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:24:03,302][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:24:04,762][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->77ms 
[2025-06-30 05:25:03,329][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:26:03,361][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:27:03,384][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:28:03,398][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:28:05,094][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 05:29:03,415][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:30:03,452][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:30:05,412][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->76ms 
[2025-06-30 05:31:03,479][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:32:03,504][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:32:05,681][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 05:33:03,535][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:34:03,571][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:34:05,945][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->77ms 
[2025-06-30 05:35:03,591][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:36:03,625][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:36:06,214][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->78ms 
[2025-06-30 05:37:03,647][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:38:03,669][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:38:06,499][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->80ms 
[2025-06-30 05:39:03,699][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:40:03,728][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:40:06,769][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 05:41:03,754][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:42:03,783][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:43:03,807][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:44:03,839][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:44:07,099][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->78ms 
[2025-06-30 05:45:03,869][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:46:03,900][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:46:07,364][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->81ms 
[2025-06-30 05:47:03,920][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:48:03,930][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:48:07,624][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->75ms 
[2025-06-30 05:49:03,953][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:50:03,986][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:50:07,884][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 05:51:04,001][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:52:04,028][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:53:04,049][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:54:04,079][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:54:08,194][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->75ms 
[2025-06-30 05:55:04,102][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:56:04,132][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:56:08,459][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 05:57:04,156][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:58:04,169][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 05:58:08,723][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->81ms 
[2025-06-30 05:59:04,184][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:00:04,210][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:01:04,224][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:02:04,239][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:02:09,048][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->77ms 
[2025-06-30 06:03:04,253][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:04:04,277][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:04:09,307][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->77ms 
[2025-06-30 06:05:04,289][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:06:04,316][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:06:09,588][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 06:07:04,329][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:08:04,344][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:08:09,851][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->80ms 
[2025-06-30 06:09:04,357][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:10:04,382][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:11:04,395][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:12:04,409][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:12:10,169][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->80ms 
[2025-06-30 06:13:04,423][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:14:04,449][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:14:10,426][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 06:15:04,464][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:15:10,154][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 06:16:04,478][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:16:10,694][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 06:17:04,490][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:18:04,504][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:18:10,957][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->79ms 
[2025-06-30 06:19:04,518][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:20:04,542][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:20:11,229][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->81ms 
[2025-06-30 06:21:04,555][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:22:04,569][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:22:11,491][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->80ms 
[2025-06-30 06:23:04,584][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:24:04,609][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:24:11,759][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->81ms 
[2025-06-30 06:25:04,621][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:26:04,648][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:27:04,661][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:28:04,674][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:28:12,074][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->75ms 
[2025-06-30 06:29:04,688][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:30:04,713][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:30:12,332][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->78ms 
[2025-06-30 06:31:04,728][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:32:04,741][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:32:12,586][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->78ms 
[2025-06-30 06:33:04,754][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:33:54,812][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-30 06:33:54,844][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-30 06:33:54,850][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-30 06:42:48,549][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-30 06:42:48,580][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 44896 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-30 06:42:48,581][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-30 06:42:49,369][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-30 06:42:49,369][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-30 06:42:49,399][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces. 
[2025-06-30 06:42:50,361][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-30 06:42:51,386][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-30 06:42:51,393][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-30 06:42:51,396][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-30 06:42:51,396][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-30 06:42:51,458][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-30 06:42:51,458][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 2846 ms 
[2025-06-30 06:42:52,035][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-30 06:42:52,363][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-30 06:42:52,620][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 06:42:53,229][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 06:42:54,085][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-30 06:42:54,395][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-30 06:42:55,149][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-30 06:42:55,204][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-30 06:42:55,210][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-30 06:42:55,239][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 7.042 seconds (process running for 7.583) 
[2025-06-30 06:42:55,610][INFO ][RMI TCP Connection(3)-192.168.50.6][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-30 06:42:55,610][INFO ][RMI TCP Connection(3)-192.168.50.6][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-30 06:42:55,613][INFO ][RMI TCP Connection(3)-192.168.50.6][o.s.w.s.DispatcherServlet:554] Completed initialization in 3 ms 
[2025-06-30 06:43:04,083][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-30 06:43:04,083][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-30 06:43:04,083][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:43:04,498][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->105ms 
[2025-06-30 06:44:04,103][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:45:04,135][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:45:04,825][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->106ms 
[2025-06-30 06:46:04,154][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:47:04,186][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:48:04,203][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:49:04,222][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:49:05,202][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 06:50:04,239][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:51:04,269][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:51:05,512][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 06:52:04,286][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:53:04,303][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:53:05,816][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-30 06:54:04,320][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:55:04,351][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:56:04,368][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:57:04,400][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:57:06,192][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-30 06:58:04,416][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:59:04,431][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 06:59:06,503][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 07:00:04,450][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:01:04,483][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:01:06,842][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->108ms 
[2025-06-30 07:02:04,502][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:03:04,518][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:04:04,537][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:05:04,571][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:05:07,243][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 07:06:04,589][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:07:04,623][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:07:07,556][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->96ms 
[2025-06-30 07:08:04,642][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:09:04,657][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:09:07,879][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 07:10:04,674][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:11:04,706][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:12:04,727][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:13:04,744][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:13:08,242][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-30 07:14:04,762][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:15:04,792][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:15:08,558][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 07:15:10,185][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 07:16:04,809][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:17:04,840][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:17:08,870][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 07:18:04,858][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:19:04,873][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:20:04,890][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:21:04,921][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:21:09,239][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 07:22:04,937][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:23:04,953][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:23:09,554][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->101ms 
[2025-06-30 07:24:04,969][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:25:05,001][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:25:09,874][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 07:26:05,018][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:27:05,049][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:28:05,067][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:29:05,083][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:29:10,239][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 07:30:05,100][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:31:05,131][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:31:10,545][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->100ms 
[2025-06-30 07:32:05,149][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:33:05,169][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:33:10,855][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 07:34:05,184][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:35:05,217][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:36:05,235][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:37:05,269][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:37:11,230][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->99ms 
[2025-06-30 07:38:05,298][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:39:05,329][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:39:11,541][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->102ms 
[2025-06-30 07:39:24,554][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-30 07:39:24,582][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-30 07:39:24,586][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
[2025-06-30 07:50:18,281][INFO ][background-preinit][o.h.v.i.u.Version:21] HV000001: Hibernate Validator 8.0.1.Final 
[2025-06-30 07:50:18,321][INFO ][main][n.l.s.a.AdminApplication:50] Starting AdminApplication using Java 17.0.13 with PID 58100 (D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3\sa-admin\target\classes started by xh in D:\work\IdeaProjects\smart-admin\smart-admin-api-java17-springboot3) 
[2025-06-30 07:50:18,324][INFO ][main][n.l.s.a.AdminApplication:660] The following 1 profile is active: "dev" 
[2025-06-30 07:50:19,385][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:295] Multiple Spring Data modules found, entering strict repository configuration mode 
[2025-06-30 07:50:19,387][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:143] Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
[2025-06-30 07:50:19,418][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate:211] Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces. 
[2025-06-30 07:50:20,441][INFO ][main][c.a.d.p.DruidDataSource:1002] {dataSource-1} inited 
[2025-06-30 07:50:21,592][INFO ][main][o.s.b.w.e.t.TomcatWebServer:111] Tomcat initialized with port 1024 (http) 
[2025-06-30 07:50:21,601][INFO ][main][o.a.c.h.Http11NioProtocol:173] Initializing ProtocolHandler ["http-nio-1024"] 
[2025-06-30 07:50:21,603][INFO ][main][o.a.c.c.StandardService:173] Starting service [Tomcat] 
[2025-06-30 07:50:21,603][INFO ][main][o.a.c.c.StandardEngine:173] Starting Servlet engine: [Apache Tomcat/10.1.25] 
[2025-06-30 07:50:21,667][INFO ][main][o.a.c.c.C.[.[.[/]:173] Initializing Spring embedded WebApplicationContext 
[2025-06-30 07:50:21,667][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext:296] Root WebApplicationContext: initialization completed in 3304 ms 
[2025-06-30 07:50:22,221][INFO ][main][n.l.s.b.m.s.c.ConfigService:61] ################# 系统配置缓存初始化完毕:2 ################### 
[2025-06-30 07:50:22,542][INFO ][main][o.r.Version:43] Redisson 3.25.0 
[2025-06-30 07:50:22,801][INFO ][redisson-netty-2-6][o.r.c.p.MasterPubSubConnectionPool:137] 1 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 07:50:23,439][INFO ][redisson-netty-2-19][o.r.c.p.MasterConnectionPool:137] 24 connections initialized for 49.235.40.39/49.235.40.39:6379 
[2025-06-30 07:50:24,370][INFO ][main][n.l.s.b.m.s.j.a.SmartJobClientManager:67] ==== SmartJob ==== client-manager init 
[2025-06-30 07:50:24,695][INFO ][main][n.l.s.b.c.UrlConfig:137] 不需要登录的URL：[/api/docs/development-guide, /login, /login/sendEmailCode/{loginName}, /login/getTwoFactorLoginFlag, /user/register, /api/docs/response-codes, /user/register/getCaptcha, /login/getCaptcha] 
[2025-06-30 07:50:25,460][INFO ][main][o.s.b.a.e.w.EndpointLinksResolver:60] Exposing 1 endpoint beneath base path '/actuator' 
[2025-06-30 07:50:25,516][INFO ][main][o.a.c.h.Http11NioProtocol:173] Starting ProtocolHandler ["http-nio-1024"] 
[2025-06-30 07:50:25,522][INFO ][main][o.s.b.w.e.t.TomcatWebServer:243] Tomcat started on port 1024 (http) with context path '/' 
[2025-06-30 07:50:25,554][INFO ][main][n.l.s.a.AdminApplication:56] Started AdminApplication in 7.732 seconds (process running for 8.336) 
[2025-06-30 07:50:26,102][INFO ][RMI TCP Connection(6)-192.168.50.6][o.a.c.c.C.[.[.[/]:173] Initializing Spring DispatcherServlet 'dispatcherServlet' 
[2025-06-30 07:50:26,102][INFO ][RMI TCP Connection(6)-192.168.50.6][o.s.w.s.DispatcherServlet:532] Initializing Servlet 'dispatcherServlet' 
[2025-06-30 07:50:26,103][INFO ][RMI TCP Connection(6)-192.168.50.6][o.s.w.s.DispatcherServlet:554] Completed initialization in 1 ms 
[2025-06-30 07:50:34,378][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务1 
[2025-06-30 07:50:34,379][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobScheduler:136] ==== SmartJob ==== add job:示例任务2 
[2025-06-30 07:50:34,379][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:50:34,763][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->97ms 
[2025-06-30 07:51:34,409][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:52:34,452][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:52:35,065][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-30 07:53:34,474][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:54:34,505][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:54:35,381][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-30 07:55:34,523][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:56:34,548][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:56:35,682][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-30 07:57:34,568][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:58:34,607][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 07:58:35,985][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-30 07:59:34,628][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:00:34,646][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:00:36,291][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->95ms 
[2025-06-30 08:01:34,669][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:02:34,701][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:02:36,581][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-30 08:03:34,718][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:04:34,748][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:04:36,893][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-30 08:05:34,764][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:06:34,785][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:07:34,812][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:08:34,840][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:08:37,246][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->86ms 
[2025-06-30 08:09:34,866][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:10:34,882][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:10:37,552][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->88ms 
[2025-06-30 08:11:34,914][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:12:34,943][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:12:37,855][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->90ms 
[2025-06-30 08:13:34,964][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:14:35,004][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:15:10,177][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务1,time-millis->0ms 
[2025-06-30 08:15:35,033][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:16:35,051][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:16:38,223][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->91ms 
[2025-06-30 08:17:35,070][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:18:35,111][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:18:38,528][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-30 08:19:35,131][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:20:35,159][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:20:38,837][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->94ms 
[2025-06-30 08:21:35,176][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:22:35,205][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:23:35,234][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:24:35,261][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:24:39,216][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-30 08:25:35,279][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:26:35,297][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:26:39,542][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-30 08:27:35,314][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:28:35,346][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:28:39,839][INFO ][SmartJobExecutor-1][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->92ms 
[2025-06-30 08:29:35,376][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:30:35,397][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:31:35,412][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:32:35,452][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:32:40,209][INFO ][SmartJobExecutor-0][n.l.s.b.m.s.j.c.SmartJobExecutor:75] ==== SmartJob ==== execute job->示例任务2,time-millis->93ms 
[2025-06-30 08:33:35,471][INFO ][SmartJobLauncher-0][n.l.s.b.m.s.j.c.SmartJobLauncher:121] ==== SmartJob ==== start/refresh job num:2->[示例任务1, 示例任务2] 
[2025-06-30 08:34:19,428][INFO ][SpringApplicationShutdownHook][n.l.s.b.m.s.j.c.SmartJobLauncher:151] ==== SmartJob ==== destroy job 
[2025-06-30 08:34:19,458][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2204] {dataSource-1} closing ... 
[2025-06-30 08:34:19,462][INFO ][SpringApplicationShutdownHook][c.a.d.p.DruidDataSource:2277] {dataSource-1} closed 
