<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.oa.enterprise.dao.EnterpriseEmployeeDao">

    <delete id="deleteByEnterpriseAndEmployeeIdList">
        delete from t_oa_enterprise_employee where enterprise_id = #{enterpriseId} and employee_id in
        <foreach collection="employeeIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByEmployeeId">
        delete from t_oa_enterprise_employee where employee_id = #{employeeId}
    </delete>

    <select id="selectByEnterpriseId"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.entity.EnterpriseEmployeeEntity">
        select * from t_oa_enterprise_employee where enterprise_id = #{enterpriseId}
    </select>

    <select id="selectByEnterpriseAndEmployeeIdList"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.entity.EnterpriseEmployeeEntity">
        select * from t_oa_enterprise_employee where enterprise_id = #{enterpriseId} and employee_id in
        <foreach collection="employeeIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByEmployeeIdList"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.vo.EnterpriseEmployeeVO">
        select
        t_oa_enterprise_employee.*,
        t_oa_enterprise.enterprise_name,
        t_employee.actual_name as employeeName
        from t_oa_enterprise_employee
        left join t_oa_enterprise  on t_oa_enterprise_employee.enterprise_id = t_oa_enterprise.enterprise_id
        left join t_employee  on t_oa_enterprise_employee.employee_id = t_employee.employee_id
        where t_oa_enterprise_employee.employee_id in
        <foreach collection="employeeIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByEnterpriseIdList"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.vo.EnterpriseEmployeeVO">
        select
        t_oa_enterprise_employee.*,
        t_oa_enterprise.enterprise_name,
        t_employee.*,
        t_employee.actual_name as employeeName
        from t_oa_enterprise_employee
        left join t_oa_enterprise  on t_oa_enterprise_employee.enterprise_id = t_oa_enterprise.enterprise_id
        left join t_employee on t_oa_enterprise_employee.employee_id = t_employee.employee_id
        where t_oa_enterprise_employee.enterprise_id in
        <foreach collection="enterpriseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectEnterpriseIdByEmployeeId" resultType="java.lang.Long">
        select enterprise_id from t_oa_enterprise_employee where employee_id = #{employeeId}
    </select>

    <select id="selectEmployeeIdByEnterpriseIdList" resultType="java.lang.Long">
        select employee_id from t_oa_enterprise_employee where enterprise_id in
        <foreach collection="enterpriseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryPageEmployeeList"
            resultType="net.lab1024.sa.admin.module.business.oa.enterprise.domain.vo.EnterpriseEmployeeVO">
        select
        t_oa_enterprise_employee.enterprise_id,
        t_oa_enterprise.enterprise_name,
        t_employee.*,
        t_employee.actual_name as employeeName
        from t_oa_enterprise_employee
        left join t_oa_enterprise  on t_oa_enterprise_employee.enterprise_id = t_oa_enterprise.enterprise_id
        left join t_employee on t_oa_enterprise_employee.employee_id = t_employee.employee_id
        where t_oa_enterprise_employee.enterprise_id = #{queryForm.enterpriseId}
        <if test="queryForm.keyword != null and queryForm.keyword != ''">
            AND (
            INSTR(t_employee.actual_name,#{queryForm.keyword})
            OR INSTR(t_employee.phone,#{queryForm.keyword})
            OR INSTR(t_employee.login_name,#{queryForm.keyword})
            )
        </if>
        <if test="queryForm.deletedFlag != null">
            AND t_employee.deleted_flag = #{queryForm.deletedFlag}
        </if>
    </select>


</mapper>