package net.lab1024.sa.base.module.support.captcha.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 图形验证码 表单
 *
 * <AUTHOR> 卓大
 * @Date 2021-09-02 20:21:10
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */

@Data
public class CaptchaForm {

    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String captchaCode;

    @Schema(description = "验证码uuid标识")
    @NotBlank(message = "验证码uuid标识不能为空")
    private String captchaUuid;
}
