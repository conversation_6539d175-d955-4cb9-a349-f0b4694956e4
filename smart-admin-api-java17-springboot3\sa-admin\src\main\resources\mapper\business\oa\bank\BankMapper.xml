<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.oa.bank.dao.BankDao">
    <update id="deleteBank">
        UPDATE t_oa_bank
        SET deleted_flag = #{deletedFlag}
        WHERE bank_id = #{bankId}
    </update>
    <select id="queryByAccountNumber"
            resultType="net.lab1024.sa.admin.module.business.oa.bank.domain.BankEntity">
        SELECT *
        FROM t_oa_bank
        WHERE enterprise_id = #{enterpriseId}
        AND account_number = #{accountNumber}
        AND deleted_flag = #{deletedFlag}
        <if test="excludeBankId != null">
            AND bank_id != #{excludeBankId}
        </if>
    </select>
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.oa.bank.domain.BankVO">
        SELECT t_oa_bank.*,
        t_oa_enterprise.enterprise_name
        FROM t_oa_bank
        LEFT JOIN t_oa_enterprise ON t_oa_bank.enterprise_id = t_oa_enterprise.enterprise_id
        <where>
            t_oa_bank.deleted_flag = #{queryForm.deletedFlag}
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (INSTR(t_oa_bank.bank_name,#{queryForm.keywords}) OR
                INSTR(t_oa_bank.account_name,#{queryForm.keywords}) OR
                INSTR(t_oa_bank.account_number,#{queryForm.keywords}) OR
                INSTR(t_oa_bank.create_user_name,#{queryForm.keywords}))
            </if>
            <if test="queryForm.startTime != null">
                AND DATE_FORMAT(t_oa_bank.create_time, '%Y-%m-%d') &gt;= #{queryForm.startTime}
            </if>
            <if test="queryForm.endTime != null">
                AND DATE_FORMAT(t_oa_bank.create_time, '%Y-%m-%d') &lt;= #{queryForm.endTime}
            </if>
            <if test="queryForm.disabledFlag != null">
                AND t_oa_bank.disabled_flag = #{queryForm.disabledFlag}
            </if>
            <if test="queryForm.enterpriseId != null">
                AND t_oa_bank.enterprise_id = #{queryForm.enterpriseId}
            </if>
        </where>
        <if test="queryForm.sortItemList == null or queryForm.sortItemList.size == 0">
            ORDER BY t_oa_bank.create_time DESC
        </if>
    </select>
    <select id="getDetail" resultType="net.lab1024.sa.admin.module.business.oa.bank.domain.BankVO">
        SELECT t_oa_bank.*,
               t_oa_enterprise.enterprise_name
        FROM t_oa_bank
                 LEFT JOIN t_oa_enterprise ON t_oa_bank.enterprise_id = t_oa_enterprise.enterprise_id
        WHERE t_oa_bank.bank_id = #{bankId}
          AND t_oa_bank.deleted_flag = #{deletedFlag}
    </select>
</mapper>