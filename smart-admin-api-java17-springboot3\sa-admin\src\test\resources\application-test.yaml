# ===============================================
# sa-plus 测试环境特有配置
# 说明：此文件中的配置会覆盖 sa-plus 和 sa-common 中的配置
# ===============================================

spring:
  # 数据库连接信息
  datasource:
    url: ***************************************************************************************************************************************************************************************************************************
    username: root
    password: xuehua123
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    initial-size: 2
    min-idle: 2
    max-active: 10
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    filters: stat
    druid:
      username: druid
      password: 1024
      login:
        enabled: false
      method:
        pointcut: net.lab1024.sa..*Service.*
  # JPA/Hibernate 配置
  jpa:
    hibernate:
      ddl-auto: none # 使用开发数据库时，不应自动创建或删除表结构
    show-sql: true # 在控制台打印SQL语句，便于调试
    properties:
      hibernate:
        format_sql: true # 格式化SQL输出
  # H2 控制台配置
  h2:
    console:
      enabled: false # 不再使用H2数据库，禁用控制台

logging:
  # 日志级别配置
  level:
    # 将项目的根 logger 设置为 INFO 级别
    net.lab1024.sa: INFO
    # 将测试相关的 SQL 日志设置为 DEBUG 级别，以查看更详细的数据库操作
    org.springframework.jdbc: DEBUG
    org.hibernate.SQL: DEBUG
    # 将NFC模块的日志级别设为DEBUG以便测试
    net.lab1024.sa.admin.module.business.nfc: DEBUG

mybatis-plus:
  # Point to the location of our mappers
  mapper-locations: classpath*:/mapper/**/*.xml
  # Let mybatis-plus handle DDL (table creation) based on entities.
  # This is perfect for testing as it sets up the schema automatically.
  global-config:
    db-config:
      # Automatically creates/updates the table schema.
      schema-change: true

sa-token:
  # 在非web环境(如单元测试)中，关闭Web上下文环境的自动装配
  is-web-context: false
  # token名称 (Same-Token机制)
  token-name: Authorization 