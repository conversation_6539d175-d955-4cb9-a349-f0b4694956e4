package net.lab1024.sa.admin.module.system.employee.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.employee.domain.form.EmployeeRegisterForm;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.captcha.CaptchaService;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 用户注册功能 集成测试
 *
 * <AUTHOR> 卓大
 * @Date 2024-07-29 22:55:49
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional // 每个测试方法执行完毕后，自动回滚数据库事务
public class UserRegisterControllerTests {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private EmployeeDao employeeDao;

    @MockBean
    private CaptchaService captchaService;

    @BeforeEach
    public void setUp() {
        // 模拟验证码校验永远成功
        Mockito.when(captchaService.checkCaptcha(any())).thenReturn(ResponseDTO.ok());
    }


    private EmployeeRegisterForm buildValidForm(String loginName, String phone) {
        EmployeeRegisterForm form = new EmployeeRegisterForm();
        form.setLoginName(loginName);
        form.setPassword("Password@123");
        form.setConfirmPassword("Password@123");
        form.setPhone(phone);
        // 添加虚拟验证码信息以通过 @Valid 校验
        form.setCaptchaUuid("test-uuid");
        form.setCaptchaCode("test-code");
        return form;
    }

    @Test
    public void testRegister_Success() throws Exception {
        EmployeeRegisterForm form = buildValidForm("success_user", "18800000001");

        mockMvc.perform(post("/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.data.token").exists())
                .andExpect(jsonPath("$.data.loginName").value(form.getLoginName()))
                .andExpect(jsonPath("$.data.actualName").value(form.getLoginName()));

        // 验证数据库中是否成功插入数据
        List<EmployeeEntity> employees = employeeDao.getByLoginName(form.getLoginName(), false);
        assertNotNull(employees, "根据登录名查询应返回列表");
        assertFalse(employees.isEmpty(), "注册成功后，返回的列表不应为空");
        EmployeeEntity employee = employees.get(0);
        assertNotNull(employee, "注册成功后，数据库中应能找到该用户");
    }

    @Test
    public void testRegister_LoginNameExists() throws Exception {
        // 直接使用数据库中已存在的登录名进行测试 (假设admin是已存在的)
        EmployeeRegisterForm duplicateForm = buildValidForm("admin", "18800000003");

        mockMvc.perform(post("/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(duplicateForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(UserErrorCode.PARAM_ERROR.getCode()))
                .andExpect(jsonPath("$.msg").value("登录名已存在"));
    }

    @Test
    public void testRegister_PhoneExists() throws Exception {
        // 直接使用数据库中已存在的手机号进行测试
        EmployeeRegisterForm duplicateForm = buildValidForm("new_user_with_existing_phone", "18822223333");

        mockMvc.perform(post("/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(duplicateForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(UserErrorCode.PARAM_ERROR.getCode()))
                .andExpect(jsonPath("$.msg").value("手机号已存在"));
    }

    @Test
    public void testRegister_PasswordMismatch() throws Exception {
        EmployeeRegisterForm form = buildValidForm("mismatch_user", "18800000005");
        form.setConfirmPassword("DifferentPassword");

        mockMvc.perform(post("/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(UserErrorCode.PARAM_ERROR.getCode()))
                .andExpect(jsonPath("$.msg").value("两次密码输入不一致"));
    }

    @Test
    public void testRegister_PasswordTooShort() throws Exception {
        EmployeeRegisterForm form = buildValidForm("short_password_user", "18800000006");
        form.setPassword("123");
        form.setConfirmPassword("123");

        mockMvc.perform(post("/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(UserErrorCode.PARAM_ERROR.getCode()))
                .andExpect(jsonPath("$.msg").value(Matchers.containsString("密码长度不能少于6位")));
    }
} 