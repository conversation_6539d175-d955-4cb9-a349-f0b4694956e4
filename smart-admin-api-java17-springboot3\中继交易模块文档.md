---

## NFC Bridge 模块 - 前后端接口对接文档 (最终版)

**文档版本**: 1.3 (最终补充版)
**最后修订**: 2025-06-30

### 1. 概述

本文档旨在为移动端App（"客户端"）开发团队提供与后端"NFC Bridge"模块进行通信所需的所有技术规范。本模块包含两部分：
1.  **WebSocket 实时通信协议**：用于处理核心的NFC中继会话，包括设备配对、APDU指令转发等。
2.  **RESTful API**：用于管理和查询非实时性数据，如用户的历史交易记录。

---

### 2. WebSocket 实时通信协议

WebSocket是本模块的核心，所有实时的中继逻辑都通过它完成。

#### 2.1 连接端点 (Endpoint)

客户端必须通过以下格式的URL来建立WebSocket连接：

`ws://<your_server_address>/ws/nfc/{user_token}`

-   `<your_server_address>`: 后端服务器的地址和端口，例如 `api.yourdomain.com`。
-   `{user_token}`: 用户通过贵司标准的用户登录接口（如 `/login`）成功登录后，从后端获取到的身份认证令牌（`token`）。**此`token`必须放在URL路径中，这是连接的唯一认证方式**。

#### 2.2 通用消息格式

所有在WebSocket通道中传输的数据，无论是客户端发送还是服务器推送，都封装为统一的JSON结构：

```json
{
  "type": "EVENT_NAME",
  "payload": { ... }
}
```

-   `type` (String): 必选。描述消息的事件类型，决定了`payload`的结构和业务含义。
-   `payload` (Object): 必选。与事件相关的具体数据载体。

**重要说明：`@class` 字段**

所有客户端发送的消息必须在 `payload` 中包含 `@class` 字段，该字段值必须是服务器端对应 Java 类的完整包名。这是 Jackson 反序列化的必要要求：

```json
{
  "type": "REGISTER_ROLE",
  "payload": {
    "@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.RoleRegisterPayload",
    "role": "TRANSMITTER"
  }
}
```

**常见错误**：使用简化名称（如 `"@class": "REGISTER_ROLE"`）将导致反序列化失败。

#### 2.3 核心交互流程 (生命周期)

一个标准的中继会话（Session）遵循以下生命周期：

1.  **连接 & 认证**：客户端使用用户的`token`建立WebSocket连接。
2.  **接收连接成功**：连接成功后，服务器会立刻推送一条 `STATUS_UPDATE` (status: `CONNECTED`) 消息，表示"认证成功，连接已就绪"。
3.  **客户端注册角色**：客户端（根据用户在UI上的选择）发送 `REGISTER_ROLE` 消息，声明自己是"读卡端"(`TRANSMITTER`)还是"模拟端"(`RECEIVER`)。
4.  **等待伙伴**：第一个注册的客户端会收到服务器推送的 `STATUS_UPDATE` (status: `WAITING_FOR_PARTNER`) 消息。此时App应显示等待动画。
5.  **配对成功**：当同一用户的第二个设备也注册了角色后，服务器会同时向**两个客户端**推送 `PAIRED` 消息。此时App应显示"配对成功，请靠近卡片/POS机"。
6.  **数据中继**：
    *   "模拟端"将从POS机读到的APDU指令，通过 `APDU_COMMAND` 消息发送给服务器。
    *   服务器将其转发给"读卡端"。
    *   "读卡端"将从物理卡收到的响应，通过 `APDU_RESPONSE` 消息发送给服务器。
    *   服务器将其转发回"模拟端"。
    *   此过程可多次重复。
7.  **会话结束**：会话可以通过多种方式结束：
    *   **客户端主动结束**：任一客户端发送 `SESSION_END` 消息（如用户点击取消，或交易成功/失败）。
    *   **意外断开**：任一客户端网络中断，服务器会向另一方推送 `STATUS_UPDATE` (status: `PARTNER_DISCONNECTED`)。
    *   服务器会向双方推送最终的 `SESSION_END` 消息，并随后关闭连接。

#### 2.4 客户端 -> 服务器 事件 (Client-to-Server Events)

| Type | Payload | 发送方 | 描述 |
| :--- | :--- | :--- | :--- |
| **`REGISTER_ROLE`** | `{"@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.RoleRegisterPayload", "role": "TRANSMITTER" \| "RECEIVER"}` | 读卡端 / 模拟端 | 声明客户端的角色，启动配对流程。 |
| **`APDU_COMMAND`** | `{"@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.ApduPayload", "apdu": "00A4..."}` | 模拟端 | 发送从POS机/读卡器接收到的APDU指令（Hex字符串）。 |
| **`APDU_RESPONSE`** | `{"@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.ApduPayload", "apdu": "9000"}` | 读卡端 | 发送从物理NFC卡片读取到的APDU响应（Hex字符串）。 |
| **`CLIENT_STATE_UPDATE`** | `{"@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.ClientStateUpdatePayload", "state": "CARD_ATTACHED"}` | 读卡端 / 模拟端 | 客户端主动上报自身状态变化。`state`必选，可选值: `CARD_ATTACHED`, `CARD_DETACHED`。 |
| **`SESSION_END`** | `{"@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.SessionEndPayload", "status": "...", "reason": "..."}` | 读卡端 / 模拟端 | 客户端主动、优雅地通知服务器会话结束。`status`必选，可选值: `SUCCESS`, `FAILED`, `CANCELLED`。`reason`可选。 |

#### 2.5 服务器 -> 客户端 事件 (Server-to-Client Events)

| Type | Payload | 接收方 | 描述与处理建议 |
| :--- | :--- | :--- | :--- |
| **`STATUS_UPDATE`** | `{"status": "...", "message": "..."}` | 读卡端 / 模拟端 | 通用的状态更新，用于驱动UI变化。`status`的可能值：<br>- `CONNECTED`: "连接认证成功"<br>- `WAITING_FOR_PARTNER`: "已就绪，正在等待您的另一台设备..."<br>- `PARTNER_DISCONNECTED`: "对方设备已断开连接"<br>- `PARTNER_CARD_ATTACHED`: "对方设备已准备就绪，请开始操作。"<br>- `PARTNER_CARD_DETACHED`: "对方设备卡片已断开。" |
| **`PAIRED`** | `null` | 读卡端 & 模拟端 | 通知双方已成功配对，中继通道建立。App应更新UI，提示用户进行下一步操作。 |
| **`APDU_COMMAND`** | `{"apdu": "00A4..."}` | 读卡端 | 服务器转发来的APDU指令。App应立即将此指令通过原生NFC接口发送给物理卡片。 |
| **`APDU_RESPONSE`** | `{"apdu": "9000"}` | 模拟端 | 服务器转发来的APDU响应。App应立即将此响应通过HCE服务返回给POS机。 |
| **`SESSION_END`** | `{"status": "...", "reason": "..."}` | 读卡端 & 模拟端 | 服务器广播的会话最终结果。App收到此消息后，应展示最终结果（成功/失败），并准备关闭连接。 |
| **`ERROR`** | `{"code": "...", "message": "..."}` | 特定客户端 | **(详情见下方错误码列表)** |

##### **`ERROR` 事件错误码详解**

当服务器向客户端发送 `type: "ERROR"` 的消息时，`payload` 中的 `code` 字段可以帮助客户端进行精确的错误处理。

| `code` | `message` (示例) | 触发时机与说明 |
| :--- | :--- | :--- |
| `INVALID_TOKEN` | "无效的凭证，请重新登录" | WebSocket连接时，URL路径中的`token`无法通过验证。App应引导用户重新登录。 |
| `INVALID_LOGIN_ID` | "无效的用户标识" | `token`有效，但无法从中解析出合法的用户ID。属于服务端异常。 |
| `USER_NOT_FOUND` | "无法获取用户信息" | `token`中的用户ID在数据库中找不到对应的用户。 |
| `SESSION_ALREADY_ACTIVE` | "连接失败：您的账号已在其他设备上建立中继连接。" | 用户尝试注册角色时，发现该用户已有另一个活跃的中继会话。 |
| `INVALID_ROLE` | "无效的角色类型" | 客户端发送的 `REGISTER_ROLE` 事件中，`role` 字段不是 `TRANSMITTER` 或 `RECEIVER`。 |
| `REGISTRATION_FAILED` | "角色注册失败" | 注册角色过程中发生未知的服务端内部错误。 |
| `PARTNER_UNAVAILABLE` | "伙伴设备已离线，无法转发数据" | 在APDU中继过程中，发现伙伴设备已断开连接。 |
| `INVALID_PAYLOAD` | "无效的会话结束参数" | 客户端发送 `SESSION_END` 事件时，其`payload`不符合规范（如缺少`status`字段）。 |
| `INVALID_MESSAGE_FORMAT` | "无效的消息格式" | 客户端发送的WebSocket消息无法被解析为标准的JSON结构。 |

---

### 3. RESTful API (交易管理)

用于查询用户的历史交易记录。

#### 3.1 通用规范

-   **基地址 (Base URL)**: `http://<your_server_address>`
-   **认证方式**: 所有接口均需授权。客户端必须在HTTP请求头的 `Authorization` 字段中携带登录时获取的JWT。格式为：`Bearer {user_token}`。
-   **标准响应格式**: 所有响应都遵循项目标准的 `ResponseDTO` 结构。

#### 3.2 接口列表

##### **获取交易历史列表**

-   **功能**: 分页获取和筛选当前登录用户的历史交易记录。
-   **Endpoint**: `/transactions/queryPage`
-   **方法**: `POST`
-   **Request Body**:
    请求体为一个JSON对象，包含以下字段用于分页和筛选。所有字段均为可选。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `pageNum` | Integer | 查询的页码，从1开始，默认为`1`。 |
| `pageSize`| Integer | 每页的数量，默认为`10`。 |
| `userId` | Long | 按用户ID筛选（通常用于管理员视角）。 |
| `keyword` | String | 模糊搜索关键字。 |
| `deletedFlag` | Boolean | 按删除状态筛选。 |
| `startDate` | String | 按开始日期筛选，格式 `YYYY-MM-DD`。 |
| `endDate` | String | 按结束日期筛选，格式 `YYYY-MM-DD`。 |

-   **Request Body 示例**:
```json
{
    "pageNum": 1,
    "pageSize": 20,
    "startDate": "2025-06-01",
    "endDate": "2025-06-30"
}
```

-   **成功响应 (200 OK)**:
    注意：响应体中的字段名如 `userNotes`, `createTime` 等均是根据后端 `TransactionsVO` 的实际定义修正的。

```json
{
    "code": 0,
    "msg": "操作成功",
    "data": {
        "total": 1,
        "list": [
            {
                "id": 43,
                "transactionUid": "8eb2acf0-b602-47a4-bfd7-4f3c36f49fee",
                "userId": 9049,
                "transmitterDeviceInfo": null,
                "receiverDeviceInfo": null,
                "status": "INTERRUPTED",
                "failureReason": "Connection closed unexpectedly",
                "startTime": "2025-06-30 13:09:07",
                "endTime": "2025-06-30 13:09:07",
                "userNotes": null,
                "createTime": "2025-06-30 13:09:07",
                "updateTime": "2025-06-30 13:09:08"
            }
        ],
        "pageNum": 1,
        "pageSize": 10,
        "totalPage": 1
    }
}
```
---

### 4. 附录

#### 4.1 枚举值定义 (Enumerations)

为保证通信的准确性，以下字段必须使用固定的枚举值。

| 字段名 | 所属事件 | 允许值 | 描述 |
| :--- | :--- | :--- | :--- |
| `role` | `REGISTER_ROLE` | `"TRANSMITTER"` | 声明为"读卡端"，负责与物理卡交互。 |
| | | `"RECEIVER"` | 声明为"模拟端"，负责模拟卡片与POS机交互。 |
| `status` | `SESSION_END` | `"SUCCESS"` | 交易/会话成功。 |
| | | `"FAILED"` | 交易/会话失败。 |
| | | `"CANCELLED"` | 用户主动取消。 |
| | | `"INTERRUPTED"` | 因网络或意外原因中断。 |

#### 4.2 WebSocket 交互流程图

下图描述了一个完整的、成功的NFC中继会话的生命周期：

```mermaid
sequenceDiagram
    participant Client_A as 读卡端 (App A)
    participant Server as NFC Bridge 服务器
    participant Client_B as 模拟端 (App B)

    Client_A->>+Server: 建立WebSocket连接 (携带Token)
    Server-->>-Client_A: STATUS_UPDATE (status: CONNECTED)

    Client_B->>+Server: 建立WebSocket连接 (携带Token)
    Server-->>-Client_B: STATUS_UPDATE (status: CONNECTED)

    Client_A->>Server: REGISTER_ROLE (role: TRANSMITTER)
    Server-->>Client_A: STATUS_UPDATE (status: WAITING_FOR_PARTNER)

    Client_B->>Server: REGISTER_ROLE (role: RECEIVER)
    Note over Server: 用户A和B配对成功!
    Server-->>Client_A: PAIRED
    Server-->>Client_B: PAIRED

    loop APDU 指令中继
        Client_B->>Server: APDU_COMMAND (来自POS机)
        Server->>Client_A: APDU_COMMAND (转发)
        Client_A->>Server: APDU_RESPONSE (来自物理卡)
        Server->>Client_B: APDU_RESPONSE (转发)
    end

    Client_B->>Server: SESSION_END (status: SUCCESS)
    Note over Server: 会话结束，更新数据库
    Server-->>Client_A: SESSION_END (status: SUCCESS, 广播)
    Server-->>Client_B: SESSION_END (status: SUCCESS, 广播)

    Server->>-Client_A: 关闭连接
    Server->>-Client_B: 关闭连接
```

</rewritten_file>