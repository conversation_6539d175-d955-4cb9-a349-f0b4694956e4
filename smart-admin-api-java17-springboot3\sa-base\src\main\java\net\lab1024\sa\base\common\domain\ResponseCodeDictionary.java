package net.lab1024.sa.base.common.domain;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 响应码字典 - 用于 Swagger 文档展示
 * 
 * 本类提供完整的响应状态码和错误级别说明，供开发者参考
 *
 * <AUTHOR> 卓大
 * @Date 2024-06-29 15:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Schema(description = "响应码字典")
public class ResponseCodeDictionary {

    @Schema(description = """
        ## 📋 完整响应状态码字典
        
        ### ✅ 成功状态码
        - **0**: 操作成功
        
        ### 🔧 系统级错误 (10001-19999, level: "system")
        > 这类错误通常是代码bug或系统配置问题，需要程序员修复
        - **10001**: 系统错误 - 系统似乎出现了点小问题
        
        ### ⚠️ 意外错误 (20001-29999, level: "unexpected") 
        > 这类错误是不可预期的异常，需要技术人员排查
        - **20001**: 业务繁忙，请稍后重试
        - **20002**: 付款单id发生了异常，请联系技术人员排查
        
        ### ❌ 用户级错误 (30001-39999, level: "user")
        > 这类错误是用户操作不当引起，用户可自行解决
        - **30001**: 参数错误
        - **30002**: 左翻右翻，数据竟然找不到了~
        - **30003**: 数据已存在了呀~
        - **30004**: 亲~您操作的太快了，请稍等下再操作~
        - **30005**: 对不起，您没有权限访问此内容哦~
        - **30006**: 系統正在紧急开发中，敬请期待~
        - **30007**: 您还未登录或登录失效，请重新登录！
        - **30008**: 用户状态异常
        - **30009**: 请勿重复提交
        - **30010**: 登录连续失败已经被锁定，无法登录
        - **30011**: 登录连续失败将会锁定提醒
        - **30012**: 长时间未操作系统，需要重新登录
        
        ## 🏷️ 错误级别说明
        
        ### level 字段取值
        - **null**: 操作成功时为空
        - **"system"**: 系统级别错误，代码bug，需要程序员修复
        - **"unexpected"**: 意外错误级别，不可预期的异常，需要技术人员排查  
        - **"user"**: 用户级别错误，用户操作不当引起，用户可自行解决
        
        ## 💡 使用建议
        
        1. **前端处理**: 根据 `level` 字段决定错误处理策略
        2. **用户提示**: `level: "user"` 的错误可直接显示 `msg` 给用户
        3. **日志记录**: `level: "system"` 和 `level: "unexpected"` 的错误需要记录详细日志
        4. **监控告警**: 系统级和意外错误应该触发监控告警
        """)
    private String codeDescription;
    
    @Schema(description = """
        ## 🎯 常见业务场景状态码
        
        ### 用户注册相关
        - **0**: 注册成功
        - **30001**: 登录名已存在 / 手机号已存在 / 密码格式不正确
        
        ### 用户登录相关  
        - **0**: 登录成功
        - **30001**: 登录名或密码错误
        - **30007**: 登录失效，需要重新登录
        - **30010**: 登录失败次数过多，账号已被锁定
        
        ### 权限验证相关
        - **30005**: 没有权限访问
        - **30007**: 未登录或登录失效
        
        ### 数据操作相关
        - **0**: 操作成功  
        - **30001**: 参数错误
        - **30002**: 数据不存在
        - **30003**: 数据已存在
        - **30004**: 操作太快，请稍等
        """)
    private String businessScenarios;
} 