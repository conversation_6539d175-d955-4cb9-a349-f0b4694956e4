[2025-06-29 22:53:04,616][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:04,622][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:04,627][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:04,636][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:04,640][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:04,643][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:05,152][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,308][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 22:53:06,646][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,650][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,673][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,683][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,693][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,707][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,717][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,721][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,730][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy140] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,745][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,750][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy141] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:06,760][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:53:12,975][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 22:54:34,089][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:34,099][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:34,104][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:34,111][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:34,117][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:34,120][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:34,565][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,597][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 22:54:35,871][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,874][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,890][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,901][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,907][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,915][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,923][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,927][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,932][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy140] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,940][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,943][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy141] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:35,946][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:54:42,633][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 22:59:26,470][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:26,478][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:26,483][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:26,490][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:26,494][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:26,496][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:26,989][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,106][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 22:59:28,406][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,410][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,431][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,443][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,456][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,467][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,476][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,480][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,486][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy140] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,495][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,498][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy141] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:28,502][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 22:59:35,282][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
[2025-06-29 23:02:01,330][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatisPlusConfig' of type [net.lab1024.sa.base.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:01,354][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:01,363][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'myBatisPlugin' of type [net.lab1024.sa.admin.module.system.datascope.MyBatisPlugin] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:01,371][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceConfig' of type [net.lab1024.sa.base.config.DataSourceConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:01,376][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'jdkRegexpMethodPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:01,392][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'defaultPointcutAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:02,040][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'druidDataSource' of type [com.alibaba.druid.pool.DruidDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,233][WARN ][main][c.b.m.c.i.m.DeleteByIds:407] [net.lab1024.sa.base.module.support.operatelog.OperateLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteByIds] 
[2025-06-29 23:02:03,579][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,584][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration' of type [org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,612][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties' of type [org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,629][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'dataSourceScriptDatabaseInitializer' of type [org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,640][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,656][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,667][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,672][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,680][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadItemDao' of type [jdk.proxy2.$Proxy195] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,690][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,693][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadResultDao' of type [jdk.proxy2.$Proxy196] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:03,698][WARN ][main][o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437] Bean 'reloadCommand' of type [net.lab1024.sa.base.module.support.reload.ReloadCommand] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [smartReloadManager]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
[2025-06-29 23:02:10,816][WARN ][main][o.s.b.a.f.FreeMarkerAutoConfiguration:66] Cannot find template location: [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false) 
