package net.lab1024.sa.admin.module.business.nfc.transactions.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * NFC中继交易记录表 VO
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:41:36
 * @Copyright xueh所有
 */
@Data
public class TransactionsVO {

    @Schema(description = "交易ID")
    private Long id;

    @Schema(description = "交易唯一标识符")
    private String transactionUid;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "读卡端设备信息")
    private String transmitterDeviceInfo;

    @Schema(description = "模拟端设备信息")
    private String receiverDeviceInfo;

    @Schema(description = "交易状态")
    private String status;

    @Schema(description = "失败原因")
    private String failureReason;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "用户备注")
    private String userNotes;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
